import { z } from 'zod';

// Backend API Types
export const TaskItemSchema = z.object({
  icon_src: z.string().optional(),
  title: z.string().optional(),
  timestamp: z.string().optional(),
  preview: z.string().optional(), // Added for sidebar preview
});

export const ChatMessageSchema = z.object({
  sender: z.enum(['user', 'manus']),
  content: z.string(),
  timestamp: z.string().optional(),
  attachments: z.array(z.object({
    filename: z.string(),
    details: z.string().optional(),
  })).optional(),
});

export const CrawledDataSchema = z.object({
  page_title: z.string().default(''),
  tasks: z.array(TaskItemSchema).default([]),
  chat_messages: z.array(ChatMessageSchema).default([]),
  profile_status: z.object({
    profile_name: z.string().optional(),
    profile_exists: z.boolean(),
    profile_path: z.string().optional(),
  }).optional(),
});

export const CrawlRequestSchema = z.object({
  url: z.string().url(),
  profile_name: z.string().optional(),
  headless: z.boolean().default(true),
  request_id: z.string().uuid(),
});

export const SetupProfileRequestSchema = z.object({
  profile_name: z.string().min(1),
  url: z.string().url().default('https://manus.im/'),
});

// Interactive Chat Types
export const ChatWithManusRequestSchema = z.object({
  message: z.string().min(1),
  task_url: z.string().url(),
  profile_name: z.string().optional(),
  request_id: z.string().uuid(),
  headless: z.boolean().default(true),
});

export const ChatResponseSchema = z.object({
  user_message: z.string(),
  manus_response: z.string(),
  timestamp: z.string(),
  attachments: z.array(z.object({
    filename: z.string(),
    details: z.string().optional(),
  })).default([]),
});

export const InteractiveChatResultSchema = z.object({
  success: z.boolean(),
  chat_response: ChatResponseSchema.optional(),
  updated_page_data: CrawledDataSchema.optional(),
  timestamp: z.string(),
  error: z.string().optional(),
});

// Inferred Types
export type TaskItem = z.infer<typeof TaskItemSchema>;
export type ChatMessage = z.infer<typeof ChatMessageSchema>;
export type CrawledData = z.infer<typeof CrawledDataSchema>;
export type CrawlRequest = z.infer<typeof CrawlRequestSchema>;
export type SetupProfileRequest = z.infer<typeof SetupProfileRequestSchema>;
export type ChatWithManusRequest = z.infer<typeof ChatWithManusRequestSchema>;
export type ChatResponse = z.infer<typeof ChatResponseSchema>;
export type InteractiveChatResult = z.infer<typeof InteractiveChatResultSchema>;

// WebSocket Message Types
export type WebSocketMessage =
  | { type: 'progress'; message: string }
  | { type: 'data'; data: CrawledData | InteractiveChatResult }
  | { type: 'error'; message: string };
