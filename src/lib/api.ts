import axios from 'axios';
import { CrawlRequest, CrawledData, SetupProfileRequest, ChatWithManusRequest } from './types';

const API_BASE_URL = process.env.BACKEND_URL || 'http://localhost:8000';

export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// API Functions
export const crawlApi = {
  // Sync crawl
  crawlUrl: async (request: Omit<CrawlRequest, 'request_id'>): Promise<CrawledData> => {
    const response = await api.post('/crawl-url/', request);
    return response.data;
  },

  // Realtime crawl (returns immediately, data comes via WebSocket)
  crawlUrlRealtime: async (request: CrawlRequest): Promise<{ status: string }> => {
    const response = await api.post('/crawl-url-realtime/', request);
    return response.data;
  },

  // Interactive chat with Man<PERSON> (NEW)
  chatWithManusRealtime: async (request: ChatWithManusRequest): Promise<{ status: string; request_id: string }> => {
    const response = await api.post('/chat-with-manus-realtime/', request);
    return response.data;
  },

  // Health check
  healthCheck: async (): Promise<{ status: string }> => {
    const response = await api.get('/health');
    return response.data;
  },
};

export const adminApi = {
  // Setup Chrome profile
  setupProfile: async (
    request: SetupProfileRequest,
    apiKey: string
  ): Promise<{ status: string; message: string }> => {
    const response = await api.post('/admin/setup-chrome-profile/', request, {
      headers: { 'X-API-KEY': apiKey },
    });
    return response.data;
  },

  // List profiles
  listProfiles: async (apiKey: string): Promise<string[]> => {
    const response = await api.get('/admin/list-profiles/', {
      headers: { 'X-API-KEY': apiKey },
    });
    return response.data;
  },

  // Delete profile
  deleteProfile: async (profileName: string, apiKey: string): Promise<{ status: string }> => {
    const response = await api.delete(`/admin/delete-profile/${profileName}`, {
      headers: { 'X-API-KEY': apiKey },
    });
    return response.data;
  },
};
