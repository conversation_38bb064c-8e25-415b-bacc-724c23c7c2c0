'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TaskItem } from '@/lib/types';
import { PanelRight, Search, Plus, Command, MoreHorizontal } from 'lucide-react';

interface SidebarProps {
  tasks: TaskItem[];
  onTaskSelect?: (task: TaskItem) => void;
  onNewTask?: () => void;
}

export function ManusStyleSidebar({ tasks, onTaskSelect, onNewTask }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  return (
    <div
      className="h-full flex flex-col bg-gradient-to-b from-background-nav to-background-menu-white border-r border-border-main shadow-xl manus-transition"
      style={{
        width: isCollapsed ? '0px' : '320px',
        transition: 'width 0.28s cubic-bezier(0.4, 0, 0.2, 1)'
      }}
    >
      {/* Header */}
      <div className="flex">
        <div className="flex items-center px-3 py-3 flex-row h-[52px] gap-1 justify-end w-full">
          <div className="flex justify-between w-full px-1 pt-2">
            <div className="relative flex">
              <button
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-fill-tsp-gray-main rounded-md manus-transition"
              >
                <PanelRight className="h-5 w-5 text-icon-secondary" />
              </button>
            </div>
            <div className="flex flex-row gap-1">
              <button className="flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-fill-tsp-gray-main rounded-md manus-transition">
                <Search className="h-5 w-5 text-icon-secondary" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* New Task Button */}
      <div className="px-4 mb-4 flex justify-center flex-shrink-0">
        <button
          onClick={onNewTask}
          className="group flex min-w-[36px] w-full items-center justify-center gap-2 rounded-xl h-[40px] bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-white cursor-pointer shadow-lg hover:shadow-xl manus-transition hover:scale-105"
        >
          <Plus className="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" />
          <span className="text-sm font-medium whitespace-nowrap truncate">
            New Task
          </span>
          <div className="flex items-center gap-1 opacity-70 group-hover:opacity-100 transition-opacity">
            <span className="flex justify-center items-center min-w-5 h-5 px-1 rounded bg-white/20 border border-white/30">
              <Command className="h-3 w-3" />
            </span>
            <span className="flex justify-center items-center w-5 h-5 px-1 rounded bg-white/20 border border-white/30 text-xs font-medium">
              K
            </span>
          </div>
        </button>
      </div>

      {/* Tasks List */}
      <ScrollArea className="flex-1 px-2 pb-5 overflow-x-hidden">
        <div className="space-y-1">
          {tasks.map((task, index) => (
            <TaskItemComponent
              key={index}
              task={task}
              onClick={() => onTaskSelect?.(task)}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

interface TaskItemComponentProps {
  task: TaskItem;
  onClick?: () => void;
}

function TaskItemComponent({ task, onClick }: TaskItemComponentProps) {
  return (
    <div
      onClick={onClick}
      className="group flex h-16 cursor-pointer items-center gap-3 rounded-xl px-3 py-2 manus-transition hover:bg-gradient-to-r hover:from-fill-tsp-gray-main hover:to-transparent border border-transparent hover:border-border-light hover:shadow-md"
    >
      {/* Task Icon */}
      <div className="relative flex-shrink-0">
        <div className="h-10 w-10 rounded-xl flex items-center justify-center relative bg-gradient-to-br from-fill-tsp-white-dark to-fill-tsp-white-main group-hover:from-primary/20 group-hover:to-primary/10 transition-all duration-300 border border-border-light group-hover:border-primary/30">
          <div className="relative h-5 w-5 object-cover brightness-0 opacity-75 dark:opacity-100 dark:brightness-100 group-hover:scale-110 transition-transform duration-300">
            {task.icon_src ? (
              <img
                alt={task.title || 'Task'}
                className="w-full h-full object-cover rounded"
                src={task.icon_src}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-primary/30 to-secondary/30 rounded" />
            )}
          </div>
        </div>
      </div>

      {/* Task Content */}
      <div className="min-w-20 flex-1 manus-transition opacity-100">
        <div className="flex items-center gap-1 overflow-x-hidden">
          <span
            className="truncate text-sm font-medium text-text-primary flex-1 min-w-0"
            title={task.title}
          >
            {task.title || 'Untitled Task'}
          </span>
          <span className="text-text-tertiary text-xs whitespace-nowrap">
            {task.timestamp}
          </span>
        </div>
        <div className="flex items-center gap-2 h-[18px] relative">
          <span
            className="min-w-0 flex-1 truncate text-xs text-text-tertiary"
            title={task.preview || task.title}
          >
            {task.preview || task.title || 'No preview available'}
          </span>
          <div className="w-[22px] h-[22px] flex rounded-[6px] items-center justify-center pointer invisible cursor-pointer bg-background-menu-white border border-border-main shadow-sm group-hover:visible touch-device:visible">
            <MoreHorizontal className="h-4 w-4 text-icon-secondary" />
          </div>
        </div>
      </div>
    </div>
  );
}
