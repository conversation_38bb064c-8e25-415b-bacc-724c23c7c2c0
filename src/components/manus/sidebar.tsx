'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TaskItem } from '@/lib/types';
import { PanelRight, Search, Plus, Command, MoreHorizontal } from 'lucide-react';

interface SidebarProps {
  tasks: TaskItem[];
  onTaskSelect?: (task: TaskItem) => void;
  onNewTask?: () => void;
}

export function ManusStyleSidebar({ tasks, onTaskSelect, onNewTask }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  return (
    <div
      className="h-full flex flex-col bg-background-nav manus-transition"
      style={{
        width: isCollapsed ? '0px' : '300px',
        transition: 'width 0.28s cubic-bezier(0.4, 0, 0.2, 1)'
      }}
    >
      {/* Header */}
      <div className="flex">
        <div className="flex items-center px-3 py-3 flex-row h-[52px] gap-1 justify-end w-full">
          <div className="flex justify-between w-full px-1 pt-2">
            <div className="relative flex">
              <button
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-fill-tsp-gray-main rounded-md manus-transition"
              >
                <PanelRight className="h-5 w-5 text-icon-secondary" />
              </button>
            </div>
            <div className="flex flex-row gap-1">
              <button className="flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-fill-tsp-gray-main rounded-md manus-transition">
                <Search className="h-5 w-5 text-icon-secondary" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* New Task Button */}
      <div className="px-3 mb-1 flex justify-center flex-shrink-0">
        <button
          onClick={onNewTask}
          className="flex min-w-[36px] w-full items-center justify-center gap-1.5 rounded-lg h-[32px] bg-Button-primary-white hover:bg-white/20 dark:hover:bg-black/60 cursor-pointer shadow-[0px_0.5px_3px_0px_var(--shadow-S)] manus-transition"
        >
          <Plus className="h-4 w-4 text-icon-primary" />
          <span className="text-sm font-medium text-text-primary whitespace-nowrap truncate">
            New task
          </span>
          <div className="flex items-center gap-0.5">
            <span className="flex text-text-tertiary justify-center items-center min-w-5 h-5 px-1 rounded-[4px] bg-fill-tsp-white-light border border-border-light">
              <Command className="h-3 w-3" />
            </span>
            <span className="flex justify-center items-center w-5 h-5 px-1 rounded-[4px] bg-fill-tsp-white-light border border-border-light text-sm font-normal text-text-tertiary">
              K
            </span>
          </div>
        </button>
      </div>

      {/* Tasks List */}
      <ScrollArea className="flex-1 px-2 pb-5 overflow-x-hidden">
        <div className="space-y-1">
          {tasks.map((task, index) => (
            <TaskItemComponent
              key={index}
              task={task}
              onClick={() => onTaskSelect?.(task)}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

interface TaskItemComponentProps {
  task: TaskItem;
  onClick?: () => void;
}

function TaskItemComponent({ task, onClick }: TaskItemComponentProps) {
  return (
    <div
      onClick={onClick}
      className="group flex h-14 cursor-pointer items-center gap-2 rounded-[10px] px-2 manus-transition hover:bg-fill-tsp-gray-main"
    >
      {/* Task Icon */}
      <div className="relative">
        <div className="h-8 w-8 rounded-full flex items-center justify-center relative bg-fill-tsp-white-dark">
          <div className="relative h-4 w-4 object-cover brightness-0 opacity-75 dark:opacity-100 dark:brightness-100">
            {task.icon_src ? (
              <img
                alt={task.title || 'Task'}
                className="w-full h-full object-cover"
                src={task.icon_src}
              />
            ) : (
              <div className="w-full h-full bg-fill-tsp-white-main rounded-full" />
            )}
          </div>
        </div>
      </div>

      {/* Task Content */}
      <div className="min-w-20 flex-1 manus-transition opacity-100">
        <div className="flex items-center gap-1 overflow-x-hidden">
          <span
            className="truncate text-sm font-medium text-text-primary flex-1 min-w-0"
            title={task.title}
          >
            {task.title || 'Untitled Task'}
          </span>
          <span className="text-text-tertiary text-xs whitespace-nowrap">
            {task.timestamp}
          </span>
        </div>
        <div className="flex items-center gap-2 h-[18px] relative">
          <span
            className="min-w-0 flex-1 truncate text-xs text-text-tertiary"
            title={task.preview || task.title}
          >
            {task.preview || task.title || 'No preview available'}
          </span>
          <div className="w-[22px] h-[22px] flex rounded-[6px] items-center justify-center pointer invisible cursor-pointer bg-background-menu-white border border-border-main shadow-sm group-hover:visible touch-device:visible">
            <MoreHorizontal className="h-4 w-4 text-icon-secondary" />
          </div>
        </div>
      </div>
    </div>
  );
}
