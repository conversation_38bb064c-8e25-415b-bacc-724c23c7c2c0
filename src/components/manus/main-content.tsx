'use client';

import { ScrollArea } from '@/components/ui/scroll-area';
import { ChatMessage } from '@/lib/types';
import { Send, Paperclip } from 'lucide-react';
import { useState } from 'react';

interface MainContentProps {
  title?: string;
  messages: ChatMessage[];
  onSendMessage?: (message: string) => void;
  isLoading?: boolean;
}

export function ManusStyleMainContent({
  title = "Manus Crawler",
  messages,
  onSendMessage,
  isLoading = false
}: MainContentProps) {
  const [inputValue, setInputValue] = useState('');

  const handleSend = () => {
    if (inputValue.trim() && onSendMessage) {
      onSendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="flex-1 flex flex-col h-full bg-background">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background border-b border-border-main">
        <div className="px-6 py-4">
          <div className="text-text-primary text-lg font-medium">
            <span className="whitespace-nowrap text-ellipsis overflow-hidden">
              {title}
            </span>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 px-6">
        <div className="space-y-4 py-4">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="text-text-tertiary text-lg mb-2">
                  No messages yet
                </div>
                <div className="text-text-tertiary text-sm">
                  Start a conversation with Manus Crawler
                </div>
              </div>
            </div>
          ) : (
            messages.map((message, index) => (
              <ChatMessageComponent key={index} message={message} />
            ))
          )}
          {isLoading && (
            <div className="flex items-center space-x-2 text-text-tertiary">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-text-tertiary"></div>
              <span>Manus is thinking...</span>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="border-t border-border-main p-4">
        <div className="flex items-end space-x-2">
          <div className="flex-1 relative">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Send message to Manus"
              className="w-full resize-none rounded-lg border border-border-main bg-background-menu-white px-4 py-3 text-text-primary placeholder-text-tertiary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent min-h-[44px] max-h-32"
              rows={1}
              disabled={isLoading}
            />
          </div>
          <button
            onClick={handleSend}
            disabled={!inputValue.trim() || isLoading}
            className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed manus-transition"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}

interface ChatMessageComponentProps {
  message: ChatMessage;
}

function ChatMessageComponent({ message }: ChatMessageComponentProps) {
  const isUser = message.sender === 'user';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-[80%] ${isUser ? 'items-end' : 'items-start'} flex flex-col`}>
        {/* Message Content */}
        <div
          className={`rounded-lg px-4 py-3 ${
            isUser
              ? 'bg-primary text-primary-foreground rounded-br-none'
              : 'bg-fill-tsp-white-main text-text-primary rounded-bl-none'
          }`}
        >
          <div className="whitespace-pre-wrap break-words">
            {message.content}
          </div>

          {/* Attachments */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="mt-2 space-y-2">
              {message.attachments.map((attachment, index) => (
                <div
                  key={index}
                  className="rounded-[10px] bg-fill-tsp-white-main border border-border-light p-3 group/attach"
                >
                  <div className="flex items-center space-x-2">
                    <Paperclip className="h-4 w-4 text-icon-secondary" />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm text-text-primary font-medium truncate">
                        {attachment.filename}
                      </div>
                      {attachment.details && (
                        <div className="text-xs text-text-tertiary">
                          {attachment.details}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Timestamp */}
        {message.timestamp && (
          <div className={`text-xs text-text-tertiary mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
            {message.timestamp}
          </div>
        )}
      </div>
    </div>
  );
}
