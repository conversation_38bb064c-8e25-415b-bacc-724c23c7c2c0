'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useCrawlerStore } from '@/stores/crawler-store';
import { useAdminStore } from '@/stores/admin-store';
import { Bug, Settings } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

interface CrawlerFormProps {
  onStartCrawl?: () => void;
}

export function CrawlerForm({ onStartCrawl }: CrawlerFormProps) {
  const [url, setUrl] = useState('https://manus.im/');
  const [selectedProfile, setSelectedProfile] = useState<string>('');
  const [headless, setHeadless] = useState(true);

  const { startCrawl, isLoading, status } = useCrawlerStore();
  const { profiles } = useAdminStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!url.trim()) return;

    const requestId = uuidv4();

    await startCrawl({
      url: url.trim(),
      profile_name: selectedProfile === '__default__' ? undefined : selectedProfile || undefined,
      headless,
      request_id: requestId
    });

    onStartCrawl?.();
  };

  return (
    <div className="w-full max-w-3xl space-y-6">
      {/* Status Display */}
      {(isLoading || status) && (
        <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-transparent">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-3">
              {isLoading && (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
              )}
              <div>
                <p className="text-sm font-medium text-primary">
                  {isLoading ? 'Crawling in progress...' : 'Ready to crawl'}
                </p>
                {status && (
                  <p className="text-xs text-text-tertiary mt-1">{status}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card className="border-border-main bg-gradient-to-br from-fill-tsp-white-main to-fill-tsp-white-dark shadow-xl">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center space-x-3 text-xl">
            <div className="p-2 rounded-lg bg-primary/10">
              <Bug className="h-6 w-6 text-primary" />
            </div>
            <span>Start Crawling</span>
          </CardTitle>
          <CardDescription className="text-text-tertiary">
            Configure and start crawling a Manus.im page with realtime updates
          </CardDescription>
        </CardHeader>
        <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* URL Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-text-primary flex items-center space-x-2">
              <span>Target URL</span>
              <span className="text-xs text-text-tertiary">(Required)</span>
            </label>
            <Input
              type="url"
              placeholder="https://manus.im/"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              required
              className="h-12 text-base border-border-main focus:border-primary focus:ring-primary/20"
            />
            <p className="text-xs text-text-tertiary">
              Enter the Manus.im page URL you want to crawl
            </p>
          </div>

          {/* Profile Selection */}
          <div>
            <label className="text-sm font-medium text-text-primary mb-2 block">
              Chrome Profile (Optional)
            </label>
            <Select value={selectedProfile} onValueChange={setSelectedProfile}>
              <SelectTrigger>
                <SelectValue placeholder="Select a profile or leave empty for default" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__default__">Default (No Profile)</SelectItem>
                {profiles.map((profile) => (
                  <SelectItem key={profile} value={profile}>
                    {profile}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Headless Mode */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="headless"
              checked={headless}
              onCheckedChange={(checked) => setHeadless(checked as boolean)}
            />
            <label
              htmlFor="headless"
              className="text-sm font-medium text-text-primary cursor-pointer"
            >
              Run in headless mode (recommended)
            </label>
          </div>

          {/* Submit Button */}
          <div className="pt-4 border-t border-border-light">
            <Button
              type="submit"
              disabled={isLoading || !url.trim()}
              className="w-full h-12 text-base font-medium bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  <span>Crawling in Progress...</span>
                </>
              ) : (
                <>
                  <Bug className="h-5 w-5 mr-3" />
                  <span>Start Crawling</span>
                </>
              )}
            </Button>
            <p className="text-xs text-text-tertiary text-center mt-3">
              Click to start crawling with realtime updates via WebSocket
            </p>
          </div>
        </form>
        </CardContent>
      </Card>
    </div>
  );
}
