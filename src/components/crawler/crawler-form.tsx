'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useCrawlerStore } from '@/stores/crawler-store';
import { useAdminStore } from '@/stores/admin-store';
import { Bug, Settings } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

interface CrawlerFormProps {
  onStartCrawl?: () => void;
}

export function CrawlerForm({ onStartCrawl }: CrawlerFormProps) {
  const [url, setUrl] = useState('https://manus.im/');
  const [selectedProfile, setSelectedProfile] = useState<string>('');
  const [headless, setHeadless] = useState(true);

  const { startCrawl, isLoading } = useCrawlerStore();
  const { profiles } = useAdminStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!url.trim()) return;

    const requestId = uuidv4();

    await startCrawl({
      url: url.trim(),
      profile_name: selectedProfile || undefined,
      headless,
      request_id: requestId
    });

    onStartCrawl?.();
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Bug className="h-5 w-5" />
          <span>Start Crawling</span>
        </CardTitle>
        <CardDescription>
          Configure and start crawling a Manus.im page
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* URL Input */}
          <div>
            <label className="text-sm font-medium text-text-primary mb-2 block">
              Target URL
            </label>
            <Input
              type="url"
              placeholder="https://manus.im/"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              required
            />
          </div>

          {/* Profile Selection */}
          <div>
            <label className="text-sm font-medium text-text-primary mb-2 block">
              Chrome Profile (Optional)
            </label>
            <Select value={selectedProfile} onValueChange={setSelectedProfile}>
              <SelectTrigger>
                <SelectValue placeholder="Select a profile or leave empty for default" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Default (No Profile)</SelectItem>
                {profiles.map((profile) => (
                  <SelectItem key={profile} value={profile}>
                    {profile}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Headless Mode */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="headless"
              checked={headless}
              onCheckedChange={(checked) => setHeadless(checked as boolean)}
            />
            <label
              htmlFor="headless"
              className="text-sm font-medium text-text-primary cursor-pointer"
            >
              Run in headless mode (recommended)
            </label>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isLoading || !url.trim()}
            className="w-full"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Crawling...
              </>
            ) : (
              <>
                <Bug className="h-4 w-4 mr-2" />
                Start Crawling
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
