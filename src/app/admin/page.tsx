'use client';

import { useState, useEffect } from 'react';
import { useAdminStore } from '@/stores/admin-store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Trash2, Plus, Key, RefreshCw, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function AdminPage() {
  const {
    apiKey,
    profiles,
    isLoading,
    error,
    setApiKey,
    loadProfiles,
    setupProfile,
    deleteProfile,
    clearError
  } = useAdminStore();

  const [showApiKeyInput, setShowApiKeyInput] = useState(!apiKey);
  const [tempApiKey, setTempApiKey] = useState('');
  const [newProfileName, setNewProfileName] = useState('');
  const [newProfileUrl, setNewProfileUrl] = useState('https://manus.im/');

  useEffect(() => {
    if (apiKey) {
      loadProfiles();
    }
  }, [apiKey, loadProfiles]);

  const handleSetApiKey = () => {
    if (tempApiKey.trim()) {
      setApiKey(tempApiKey.trim());
      setShowApiKeyInput(false);
      setTempApiKey('');
    }
  };

  const handleCreateProfile = async () => {
    if (newProfileName.trim()) {
      const success = await setupProfile({
        profile_name: newProfileName.trim(),
        url: newProfileUrl
      });
      if (success) {
        setNewProfileName('');
        setNewProfileUrl('https://manus.im/');
      }
    }
  };

  const handleDeleteProfile = async (profileName: string) => {
    if (confirm(`Are you sure you want to delete profile "${profileName}"?`)) {
      await deleteProfile(profileName);
    }
  };

  return (
    <div className="flex-1 p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-text-primary">Admin Dashboard</h1>
            <p className="text-text-secondary">Manage Chrome profiles and crawler settings</p>
          </div>
        </div>
        <Button
          onClick={() => loadProfiles()}
          disabled={isLoading || !apiKey}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-destructive">{error}</p>
            <Button variant="ghost" size="sm" onClick={clearError}>
              ×
            </Button>
          </div>
        </div>
      )}

      {/* API Key Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5" />
            <span>API Key Configuration</span>
          </CardTitle>
          <CardDescription>
            Enter your admin API key to manage Chrome profiles
          </CardDescription>
        </CardHeader>
        <CardContent>
          {showApiKeyInput ? (
            <div className="flex space-x-2">
              <Input
                type="password"
                placeholder="Enter admin API key"
                value={tempApiKey}
                onChange={(e) => setTempApiKey(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSetApiKey()}
              />
              <Button onClick={handleSetApiKey} disabled={!tempApiKey.trim()}>
                Set Key
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">API Key is configured</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowApiKeyInput(true)}
              >
                Change Key
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Profile Management */}
      {apiKey && (
        <>
          {/* Create New Profile */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Plus className="h-5 w-5" />
                <span>Create New Profile</span>
              </CardTitle>
              <CardDescription>
                Set up a new Chrome profile for crawling
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-text-primary mb-2 block">
                    Profile Name
                  </label>
                  <Input
                    placeholder="e.g., my-profile"
                    value={newProfileName}
                    onChange={(e) => setNewProfileName(e.target.value)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-text-primary mb-2 block">
                    Initial URL
                  </label>
                  <Input
                    placeholder="https://manus.im/"
                    value={newProfileUrl}
                    onChange={(e) => setNewProfileUrl(e.target.value)}
                  />
                </div>
              </div>
              <Button
                onClick={handleCreateProfile}
                disabled={isLoading || !newProfileName.trim()}
                className="w-full md:w-auto"
              >
                {isLoading ? 'Creating...' : 'Create Profile'}
              </Button>
            </CardContent>
          </Card>

          {/* Existing Profiles */}
          <Card>
            <CardHeader>
              <CardTitle>Existing Profiles ({profiles.length})</CardTitle>
              <CardDescription>
                Manage your Chrome profiles for crawling
              </CardDescription>
            </CardHeader>
            <CardContent>
              {profiles.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-text-tertiary">No profiles found</p>
                  <p className="text-text-tertiary text-sm">Create a new profile to get started</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {profiles.map((profile) => (
                    <div
                      key={profile}
                      className="flex items-center justify-between p-3 rounded-lg bg-fill-tsp-white-main border border-border-light"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
                          <span className="text-sm font-medium text-primary">
                            {profile.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <span className="text-text-primary font-medium">{profile}</span>
                      </div>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteProfile(profile)}
                        disabled={isLoading}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
