'use client';

import { useState, useEffect } from 'react';
import { ManusStyleSidebar } from '@/components/manus/sidebar';
import { ManusStyleMainContent } from '@/components/manus/main-content';
import { CrawlerForm } from '@/components/crawler/crawler-form';
import { useCrawlerStore } from '@/stores/crawler-store';
import { useAdminStore } from '@/stores/admin-store';
import { useWebSocket } from '@/hooks/use-websocket';
import { TaskItem, ChatMessage, CrawledData, InteractiveChatResult } from '@/lib/types';
import { v4 as uuidv4 } from 'uuid';

export default function CrawlerPage() {
  const {
    results,
    isLoading,
    currentRequestId,
    setResults,
    setStatus,
    setError,
    startChat
  } = useCrawlerStore();

  const { loadProfiles } = useAdminStore();
  const [selectedTask, setSelectedTask] = useState<TaskItem | null>(null);
  const [showForm, setShowForm] = useState(true);

  // WebSocket connection
  const { lastMessage, isConnected } = useWebSocket(currentRequestId);

  // Load profiles on mount
  useEffect(() => {
    loadProfiles();
  }, [loadProfiles]);

  // Handle WebSocket messages
  useEffect(() => {
    if (lastMessage) {
      switch (lastMessage.type) {
        case 'progress':
          setStatus(lastMessage.message);
          break;
        case 'data':
          if (lastMessage.data) {
            // Check if it's crawled data or chat result
            if ('chat_response' in lastMessage.data) {
              // It's an interactive chat result
              const chatResult = lastMessage.data as InteractiveChatResult;
              if (chatResult.updated_page_data) {
                setResults(chatResult.updated_page_data);
              }
            } else {
              // It's regular crawled data
              setResults(lastMessage.data as CrawledData);
              setShowForm(false); // Hide form after successful crawl
            }
          }
          break;
        case 'error':
          setError(lastMessage.message);
          break;
      }
    }
  }, [lastMessage, setResults, setStatus, setError]);

  // Convert crawled data to display format
  const tasks: TaskItem[] = results?.tasks || [];
  const messages: ChatMessage[] = results?.chat_messages || [];

  const handleTaskSelect = (task: TaskItem) => {
    setSelectedTask(task);
  };

  const handleNewTask = () => {
    setShowForm(true);
    setSelectedTask(null);
  };

  const handleStartCrawl = () => {
    setShowForm(false);
  };

  const handleSendMessage = async (message: string) => {
    if (!results?.page_title) return;

    const requestId = uuidv4();

    await startChat({
      message,
      task_url: window.location.href, // Current page URL
      profile_name: undefined, // Could be made configurable
      request_id: requestId,
      headless: true
    });
  };

  return (
    <div className="flex w-full h-screen overflow-hidden">
      {/* Sidebar */}
      <ManusStyleSidebar
        tasks={tasks}
        onTaskSelect={handleTaskSelect}
        onNewTask={handleNewTask}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {showForm ? (
          <div className="flex-1 flex items-center justify-center p-6">
            <CrawlerForm onStartCrawl={handleStartCrawl} />
          </div>
        ) : (
          <ManusStyleMainContent
            title={selectedTask?.title || results?.page_title || "Manus Crawler"}
            messages={messages}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
          />
        )}
      </div>
    </div>
  );
}
