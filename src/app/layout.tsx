import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Manus Crawler - NextJS Frontend",
  description: "Modern frontend for Manus.im crawler with realtime updates",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={inter.className}>
        <div className="flex w-full h-screen overflow-hidden bg-background">
          {children}
        </div>
      </body>
    </html>
  );
}
