import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Bug, Settings, Home } from "lucide-react";

export default function HomePage() {
  return (
    <div className="flex-1 flex items-center justify-center p-6">
      <div className="max-w-4xl w-full space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2">
            <Bug className="h-8 w-8 text-primary" />
            <h1 className="text-4xl font-bold text-text-primary">Manus Crawler</h1>
          </div>
          <p className="text-xl text-text-secondary max-w-2xl mx-auto">
            Modern NextJS frontend for Manus.im crawler with realtime updates and interactive chat
          </p>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="manus-hover manus-transition">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bug className="h-5 w-5" />
                <span>Crawler Interface</span>
              </CardTitle>
              <CardDescription>
                Crawl Manus.im pages with realtime updates and WebSocket integration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full">
                <Link href="/crawler">
                  Start Crawling
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="manus-hover manus-transition">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Admin Dashboard</span>
              </CardTitle>
              <CardDescription>
                Manage Chrome profiles and configure crawler settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="outline" className="w-full">
                <Link href="/admin">
                  Admin Panel
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Features List */}
        <div className="bg-fill-tsp-white-main rounded-lg p-6 space-y-4">
          <h2 className="text-2xl font-semibold text-text-primary">Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-text-secondary">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>NextJS 14+ App Router with TypeScript</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Realtime WebSocket updates</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Manus-style UI with dark theme</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Interactive chat with Manus</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Chrome profile management</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Type-safe API integration</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
