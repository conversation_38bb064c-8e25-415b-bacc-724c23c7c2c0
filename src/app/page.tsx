import Link from "next/link";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Bug, Settings, Home } from "lucide-react";

interface FeatureItemProps {
  icon: string;
  title: string;
  description: string;
}

function FeatureItem({ icon, title, description }: FeatureItemProps) {
  return (
    <div className="group p-4 rounded-xl bg-fill-tsp-white-dark/50 hover:bg-fill-tsp-white-dark border border-border-light hover:border-primary/20 transition-all duration-300 hover:scale-105">
      <div className="flex items-start space-x-3">
        <div className="text-2xl group-hover:scale-110 transition-transform duration-300">
          {icon}
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-text-primary group-hover:text-primary transition-colors duration-300">
            {title}
          </h3>
          <p className="text-sm text-text-tertiary mt-1 leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}

export default function HomePage() {
  return (
    <div className="flex-1 flex items-center justify-center p-6 bg-gradient-to-br from-background via-background to-background-menu-white">
      <div className="max-w-6xl w-full space-y-12">
        {/* Header */}
        <div className="text-center space-y-6 animate-in fade-in-0 duration-1000">
          <div className="flex items-center justify-center space-x-3 group">
            <div className="relative">
              <Bug className="h-12 w-12 text-primary transition-transform group-hover:scale-110 duration-300" />
              <div className="absolute inset-0 h-12 w-12 bg-primary/20 rounded-full blur-xl animate-pulse"></div>
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-text-primary to-text-secondary bg-clip-text text-transparent">
              Manus Crawler
            </h1>
          </div>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
            Modern NextJS frontend for Manus.im crawler with realtime updates, interactive chat,
            and comprehensive Chrome profile management
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-text-tertiary">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>System Online</span>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 animate-in slide-in-from-bottom-4 duration-1000 delay-300">
          <Card className="group relative overflow-hidden border-border-main bg-gradient-to-br from-fill-tsp-white-main to-fill-tsp-white-dark hover:from-fill-tsp-white-dark hover:to-fill-tsp-white-main transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-primary/10">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <CardHeader className="relative z-10">
              <CardTitle className="flex items-center space-x-3 text-lg">
                <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300">
                  <Bug className="h-6 w-6 text-primary group-hover:scale-110 transition-transform duration-300" />
                </div>
                <span className="group-hover:text-primary transition-colors duration-300">Crawler Interface</span>
              </CardTitle>
              <CardDescription className="text-text-tertiary leading-relaxed">
                Crawl Manus.im pages with realtime updates, WebSocket integration, and interactive chat capabilities
              </CardDescription>
            </CardHeader>
            <CardContent className="relative z-10">
              <Button asChild className="w-full group-hover:bg-primary/90 transition-all duration-300 shadow-lg hover:shadow-xl">
                <Link href="/crawler">
                  <span className="flex items-center justify-center space-x-2">
                    <span>Start Crawling</span>
                    <Bug className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </span>
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="group relative overflow-hidden border-border-main bg-gradient-to-br from-fill-tsp-white-main to-fill-tsp-white-dark hover:from-fill-tsp-white-dark hover:to-fill-tsp-white-main transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-secondary/10">
            <div className="absolute inset-0 bg-gradient-to-r from-secondary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <CardHeader className="relative z-10">
              <CardTitle className="flex items-center space-x-3 text-lg">
                <div className="p-2 rounded-lg bg-secondary/10 group-hover:bg-secondary/20 transition-colors duration-300">
                  <Settings className="h-6 w-6 text-secondary group-hover:scale-110 transition-transform duration-300" />
                </div>
                <span className="group-hover:text-secondary transition-colors duration-300">Admin Dashboard</span>
              </CardTitle>
              <CardDescription className="text-text-tertiary leading-relaxed">
                Manage Chrome profiles, configure crawler settings, and monitor system performance
              </CardDescription>
            </CardHeader>
            <CardContent className="relative z-10">
              <Button asChild variant="outline" className="w-full border-secondary/20 hover:border-secondary hover:bg-secondary/10 transition-all duration-300 shadow-lg hover:shadow-xl">
                <Link href="/admin">
                  <span className="flex items-center justify-center space-x-2">
                    <span>Admin Panel</span>
                    <Settings className="h-4 w-4 transition-transform group-hover:rotate-90" />
                  </span>
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Features List */}
        <div className="relative overflow-hidden bg-gradient-to-br from-fill-tsp-white-main via-fill-tsp-white-dark to-fill-tsp-white-main rounded-2xl p-8 border border-border-light animate-in slide-in-from-bottom-6 duration-1000 delay-500">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 opacity-50"></div>
          <div className="relative z-10">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-text-primary mb-2">Powerful Features</h2>
              <p className="text-text-secondary">Built with modern technologies for optimal performance</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <FeatureItem
                icon="⚡"
                title="NextJS 14+ App Router"
                description="Modern React framework with TypeScript support"
              />
              <FeatureItem
                icon="🔄"
                title="Realtime WebSocket"
                description="Live updates and progress tracking"
              />
              <FeatureItem
                icon="🎨"
                title="Manus-style UI"
                description="Dark theme matching Manus.im aesthetic"
              />
              <FeatureItem
                icon="💬"
                title="Interactive Chat"
                description="Communicate with Manus during crawling"
              />
              <FeatureItem
                icon="🔧"
                title="Profile Management"
                description="Chrome profiles and settings control"
              />
              <FeatureItem
                icon="🛡️"
                title="Type-safe API"
                description="Zod validation and error handling"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
