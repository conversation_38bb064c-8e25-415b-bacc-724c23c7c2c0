@import "tailwindcss";

/* Manus-style CSS Variables */
:root {
  /* Dark theme colors matching Manus.im */
  --background-nav: #1a1a1a;
  --background-menu-white: #2a2a2a;
  --fill-tsp-gray-main: rgba(255, 255, 255, 0.1);
  --fill-tsp-white-main: #3a3a3a;
  --fill-tsp-white-dark: #2a2a2a;
  --fill-tsp-white-light: #4a4a4a;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #888888;
  --icon-primary: #ffffff;
  --icon-secondary: #cccccc;
  --border-main: rgba(255, 255, 255, 0.2);
  --border-light: rgba(255, 255, 255, 0.1);
  --shadow-S: 0 1px 3px rgba(0, 0, 0, 0.3);
  --Button-primary-white: #ffffff;

  /* Standard shadcn variables */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
  --radius: 0.5rem;
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Base styles */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Manus-style scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--fill-tsp-gray-main);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Manus-style transitions */
.manus-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Manus-style hover effects */
.manus-hover:hover {
  background-color: var(--fill-tsp-gray-main);
}
