import { create } from 'zustand';
import { adminApi } from '@/lib/api';
import { SetupProfileRequest } from '@/lib/types';

interface AdminState {
  apiKey: string;
  profiles: string[];
  isLoading: boolean;
  error: string | null;

  // Actions
  setApiKey: (key: string) => void;
  loadProfiles: () => Promise<void>;
  setupProfile: (request: SetupProfileRequest) => Promise<boolean>;
  deleteProfile: (profileName: string) => Promise<boolean>;
  clearError: () => void;
}

export const useAdminStore = create<AdminState>((set, get) => ({
  apiKey: '',
  profiles: [],
  isLoading: false,
  error: null,

  setApiKey: (key: string) => {
    set({ apiKey: key });
  },

  loadProfiles: async () => {
    const { apiKey } = get();
    if (!apiKey) {
      set({ error: 'API Key is required' });
      return;
    }

    set({ isLoading: true, error: null });
    try {
      const profiles = await adminApi.listProfiles(apiKey);
      // Ensure profiles is always an array
      set({ profiles: Array.isArray(profiles) ? profiles : [], isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to load profiles',
        isLoading: false,
        profiles: [] // Reset to empty array on error
      });
    }
  },

  setupProfile: async (request: SetupProfileRequest) => {
    const { apiKey } = get();
    if (!apiKey) {
      set({ error: 'API Key is required' });
      return false;
    }

    set({ isLoading: true, error: null });
    try {
      await adminApi.setupProfile(request, apiKey);
      await get().loadProfiles(); // Reload profiles
      set({ isLoading: false });
      return true;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to setup profile',
        isLoading: false
      });
      return false;
    }
  },

  deleteProfile: async (profileName: string) => {
    const { apiKey } = get();
    if (!apiKey) {
      set({ error: 'API Key is required' });
      return false;
    }

    set({ isLoading: true, error: null });
    try {
      await adminApi.deleteProfile(profileName, apiKey);
      await get().loadProfiles(); // Reload profiles
      set({ isLoading: false });
      return true;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to delete profile',
        isLoading: false
      });
      return false;
    }
  },

  clearError: () => {
    set({ error: null });
  },
}));
