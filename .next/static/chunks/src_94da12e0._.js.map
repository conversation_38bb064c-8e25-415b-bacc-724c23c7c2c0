{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/manus/sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { TaskItem } from '@/lib/types';\nimport { PanelRight, Search, Plus, Command, MoreHorizontal } from 'lucide-react';\n\ninterface SidebarProps {\n  tasks: TaskItem[];\n  onTaskSelect?: (task: TaskItem) => void;\n  onNewTask?: () => void;\n}\n\nexport function ManusStyleSidebar({ tasks, onTaskSelect, onNewTask }: SidebarProps) {\n  const [isCollapsed, setIsCollapsed] = useState(false);\n\n  return (\n    <div\n      className=\"h-full flex flex-col bg-background-nav manus-transition\"\n      style={{\n        width: isCollapsed ? '0px' : '300px',\n        transition: 'width 0.28s cubic-bezier(0.4, 0, 0.2, 1)'\n      }}\n    >\n      {/* Header */}\n      <div className=\"flex\">\n        <div className=\"flex items-center px-3 py-3 flex-row h-[52px] gap-1 justify-end w-full\">\n          <div className=\"flex justify-between w-full px-1 pt-2\">\n            <div className=\"relative flex\">\n              <button\n                onClick={() => setIsCollapsed(!isCollapsed)}\n                className=\"flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-fill-tsp-gray-main rounded-md manus-transition\"\n              >\n                <PanelRight className=\"h-5 w-5 text-icon-secondary\" />\n              </button>\n            </div>\n            <div className=\"flex flex-row gap-1\">\n              <button className=\"flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-fill-tsp-gray-main rounded-md manus-transition\">\n                <Search className=\"h-5 w-5 text-icon-secondary\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* New Task Button */}\n      <div className=\"px-3 mb-1 flex justify-center flex-shrink-0\">\n        <button\n          onClick={onNewTask}\n          className=\"flex min-w-[36px] w-full items-center justify-center gap-1.5 rounded-lg h-[32px] bg-Button-primary-white hover:bg-white/20 dark:hover:bg-black/60 cursor-pointer shadow-[0px_0.5px_3px_0px_var(--shadow-S)] manus-transition\"\n        >\n          <Plus className=\"h-4 w-4 text-icon-primary\" />\n          <span className=\"text-sm font-medium text-text-primary whitespace-nowrap truncate\">\n            New task\n          </span>\n          <div className=\"flex items-center gap-0.5\">\n            <span className=\"flex text-text-tertiary justify-center items-center min-w-5 h-5 px-1 rounded-[4px] bg-fill-tsp-white-light border border-border-light\">\n              <Command className=\"h-3 w-3\" />\n            </span>\n            <span className=\"flex justify-center items-center w-5 h-5 px-1 rounded-[4px] bg-fill-tsp-white-light border border-border-light text-sm font-normal text-text-tertiary\">\n              K\n            </span>\n          </div>\n        </button>\n      </div>\n\n      {/* Tasks List */}\n      <ScrollArea className=\"flex-1 px-2 pb-5 overflow-x-hidden\">\n        <div className=\"space-y-1\">\n          {tasks.map((task, index) => (\n            <TaskItemComponent\n              key={index}\n              task={task}\n              onClick={() => onTaskSelect?.(task)}\n            />\n          ))}\n        </div>\n      </ScrollArea>\n    </div>\n  );\n}\n\ninterface TaskItemComponentProps {\n  task: TaskItem;\n  onClick?: () => void;\n}\n\nfunction TaskItemComponent({ task, onClick }: TaskItemComponentProps) {\n  return (\n    <div\n      onClick={onClick}\n      className=\"group flex h-14 cursor-pointer items-center gap-2 rounded-[10px] px-2 manus-transition hover:bg-fill-tsp-gray-main\"\n    >\n      {/* Task Icon */}\n      <div className=\"relative\">\n        <div className=\"h-8 w-8 rounded-full flex items-center justify-center relative bg-fill-tsp-white-dark\">\n          <div className=\"relative h-4 w-4 object-cover brightness-0 opacity-75 dark:opacity-100 dark:brightness-100\">\n            {task.icon_src ? (\n              <img\n                alt={task.title || 'Task'}\n                className=\"w-full h-full object-cover\"\n                src={task.icon_src}\n              />\n            ) : (\n              <div className=\"w-full h-full bg-fill-tsp-white-main rounded-full\" />\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Task Content */}\n      <div className=\"min-w-20 flex-1 manus-transition opacity-100\">\n        <div className=\"flex items-center gap-1 overflow-x-hidden\">\n          <span\n            className=\"truncate text-sm font-medium text-text-primary flex-1 min-w-0\"\n            title={task.title}\n          >\n            {task.title || 'Untitled Task'}\n          </span>\n          <span className=\"text-text-tertiary text-xs whitespace-nowrap\">\n            {task.timestamp}\n          </span>\n        </div>\n        <div className=\"flex items-center gap-2 h-[18px] relative\">\n          <span\n            className=\"min-w-0 flex-1 truncate text-xs text-text-tertiary\"\n            title={task.preview || task.title}\n          >\n            {task.preview || task.title || 'No preview available'}\n          </span>\n          <div className=\"w-[22px] h-[22px] flex rounded-[6px] items-center justify-center pointer invisible cursor-pointer bg-background-menu-white border border-border-main shadow-sm group-hover:visible touch-device:visible\">\n            <MoreHorizontal className=\"h-4 w-4 text-icon-secondary\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;AAcO,SAAS,kBAAkB,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAgB;;IAChF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YACL,OAAO,cAAc,QAAQ;YAC7B,YAAY;QACd;;0BAGA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;8CAEV,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAG1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAmE;;;;;;sCAGnF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACd,cAAA,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCAAK,WAAU;8CAAwJ;;;;;;;;;;;;;;;;;;;;;;;0BAQ9K,6LAAC,6IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;4BAEC,MAAM;4BACN,SAAS,IAAM,eAAe;2BAFzB;;;;;;;;;;;;;;;;;;;;;AASnB;GAnEgB;KAAA;AA0EhB,SAAS,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAA0B;IAClE,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,QAAQ,iBACZ,6LAAC;4BACC,KAAK,KAAK,KAAK,IAAI;4BACnB,WAAU;4BACV,KAAK,KAAK,QAAQ;;;;;iDAGpB,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO,KAAK,KAAK;0CAEhB,KAAK,KAAK,IAAI;;;;;;0CAEjB,6LAAC;gCAAK,WAAU;0CACb,KAAK,SAAS;;;;;;;;;;;;kCAGnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO,KAAK,OAAO,IAAI,KAAK,KAAK;0CAEhC,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI;;;;;;0CAEjC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAlDS", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/manus/main-content.tsx"], "sourcesContent": ["'use client';\n\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { ChatMessage } from '@/lib/types';\nimport { Send, Paperclip } from 'lucide-react';\nimport { useState } from 'react';\n\ninterface MainContentProps {\n  title?: string;\n  messages: ChatMessage[];\n  onSendMessage?: (message: string) => void;\n  isLoading?: boolean;\n}\n\nexport function ManusStyleMainContent({\n  title = \"Manus Crawler\",\n  messages,\n  onSendMessage,\n  isLoading = false\n}: MainContentProps) {\n  const [inputValue, setInputValue] = useState('');\n\n  const handleSend = () => {\n    if (inputValue.trim() && onSendMessage) {\n      onSendMessage(inputValue.trim());\n      setInputValue('');\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSend();\n    }\n  };\n\n  return (\n    <div className=\"flex-1 flex flex-col h-full bg-background\">\n      {/* Header */}\n      <div className=\"sticky top-0 z-10 bg-background border-b border-border-main\">\n        <div className=\"px-6 py-4\">\n          <div className=\"text-text-primary text-lg font-medium\">\n            <span className=\"whitespace-nowrap text-ellipsis overflow-hidden\">\n              {title}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Messages Area */}\n      <ScrollArea className=\"flex-1 px-6\">\n        <div className=\"space-y-4 py-4\">\n          {messages.length === 0 ? (\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"text-center\">\n                <div className=\"text-text-tertiary text-lg mb-2\">\n                  No messages yet\n                </div>\n                <div className=\"text-text-tertiary text-sm\">\n                  Start a conversation with Manus Crawler\n                </div>\n              </div>\n            </div>\n          ) : (\n            messages.map((message, index) => (\n              <ChatMessageComponent key={index} message={message} />\n            ))\n          )}\n          {isLoading && (\n            <div className=\"flex items-center space-x-2 text-text-tertiary\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-text-tertiary\"></div>\n              <span>Manus is thinking...</span>\n            </div>\n          )}\n        </div>\n      </ScrollArea>\n\n      {/* Input Area */}\n      <div className=\"border-t border-border-main p-4\">\n        <div className=\"flex items-end space-x-2\">\n          <div className=\"flex-1 relative\">\n            <textarea\n              value={inputValue}\n              onChange={(e) => setInputValue(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Send message to Manus\"\n              className=\"w-full resize-none rounded-lg border border-border-main bg-background-menu-white px-4 py-3 text-text-primary placeholder-text-tertiary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent min-h-[44px] max-h-32\"\n              rows={1}\n              disabled={isLoading}\n            />\n          </div>\n          <button\n            onClick={handleSend}\n            disabled={!inputValue.trim() || isLoading}\n            className=\"flex items-center justify-center w-10 h-10 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed manus-transition\"\n          >\n            <Send className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface ChatMessageComponentProps {\n  message: ChatMessage;\n}\n\nfunction ChatMessageComponent({ message }: ChatMessageComponentProps) {\n  const isUser = message.sender === 'user';\n\n  return (\n    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>\n      <div className={`max-w-[80%] ${isUser ? 'items-end' : 'items-start'} flex flex-col`}>\n        {/* Message Content */}\n        <div\n          className={`rounded-lg px-4 py-3 ${\n            isUser\n              ? 'bg-primary text-primary-foreground rounded-br-none'\n              : 'bg-fill-tsp-white-main text-text-primary rounded-bl-none'\n          }`}\n        >\n          <div className=\"whitespace-pre-wrap break-words\">\n            {message.content}\n          </div>\n\n          {/* Attachments */}\n          {message.attachments && message.attachments.length > 0 && (\n            <div className=\"mt-2 space-y-2\">\n              {message.attachments.map((attachment, index) => (\n                <div\n                  key={index}\n                  className=\"rounded-[10px] bg-fill-tsp-white-main border border-border-light p-3 group/attach\"\n                >\n                  <div className=\"flex items-center space-x-2\">\n                    <Paperclip className=\"h-4 w-4 text-icon-secondary\" />\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"text-sm text-text-primary font-medium truncate\">\n                        {attachment.filename}\n                      </div>\n                      {attachment.details && (\n                        <div className=\"text-xs text-text-tertiary\">\n                          {attachment.details}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Timestamp */}\n        {message.timestamp && (\n          <div className={`text-xs text-text-tertiary mt-1 ${isUser ? 'text-right' : 'text-left'}`}>\n            {message.timestamp}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;;;AALA;;;;AAcO,SAAS,sBAAsB,EACpC,QAAQ,eAAe,EACvB,QAAQ,EACR,aAAa,EACb,YAAY,KAAK,EACA;;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB,IAAI,WAAW,IAAI,MAAM,eAAe;YACtC,cAAc,WAAW,IAAI;YAC7B,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;0BAOT,6LAAC,6IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAkC;;;;;;kDAGjD,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;mCAMhD,SAAS,GAAG,CAAC,CAAC,SAAS,sBACrB,6LAAC;gCAAiC,SAAS;+BAAhB;;;;;wBAG9B,2BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,YAAY;gCACZ,aAAY;gCACZ,WAAU;gCACV,MAAM;gCACN,UAAU;;;;;;;;;;;sCAGd,6LAAC;4BACC,SAAS;4BACT,UAAU,CAAC,WAAW,IAAI,MAAM;4BAChC,WAAU;sCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GAxFgB;KAAA;AA8FhB,SAAS,qBAAqB,EAAE,OAAO,EAA6B;IAClE,MAAM,SAAS,QAAQ,MAAM,KAAK;IAElC,qBACE,6LAAC;QAAI,WAAW,CAAC,KAAK,EAAE,SAAS,gBAAgB,iBAAiB;kBAChE,cAAA,6LAAC;YAAI,WAAW,CAAC,YAAY,EAAE,SAAS,cAAc,cAAc,cAAc,CAAC;;8BAEjF,6LAAC;oBACC,WAAW,CAAC,qBAAqB,EAC/B,SACI,uDACA,4DACJ;;sCAEF,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,OAAO;;;;;;wBAIjB,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,mBACnD,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBACpC,6LAAC;oCAEC,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,WAAW,QAAQ;;;;;;oDAErB,WAAW,OAAO,kBACjB,6LAAC;wDAAI,WAAU;kEACZ,WAAW,OAAO;;;;;;;;;;;;;;;;;;mCAXtB;;;;;;;;;;;;;;;;gBAuBd,QAAQ,SAAS,kBAChB,6LAAC;oBAAI,WAAW,CAAC,gCAAgC,EAAE,SAAS,eAAe,aAAa;8BACrF,QAAQ,SAAS;;;;;;;;;;;;;;;;;AAM9B;MAtDS", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1150, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1202, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { CrawlRequest, CrawledData, SetupProfileRequest, ChatWithManusRequest } from './types';\n\nconst API_BASE_URL = process.env.BACKEND_URL || 'http://localhost:8000';\n\nexport const api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n});\n\n// API Functions\nexport const crawlApi = {\n  // Sync crawl\n  crawlUrl: async (request: Omit<CrawlRequest, 'request_id'>): Promise<CrawledData> => {\n    const response = await api.post('/crawl-url/', request);\n    return response.data;\n  },\n\n  // Realtime crawl (returns immediately, data comes via WebSocket)\n  crawlUrlRealtime: async (request: CrawlRequest): Promise<{ status: string }> => {\n    const response = await api.post('/crawl-url-realtime/', request);\n    return response.data;\n  },\n\n  // Interactive chat with Man<PERSON> (NEW)\n  chatWithManusRealtime: async (request: ChatWithManusRequest): Promise<{ status: string; request_id: string }> => {\n    const response = await api.post('/chat-with-manus-realtime/', request);\n    return response.data;\n  },\n\n  // Health check\n  healthCheck: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n};\n\nexport const adminApi = {\n  // Setup Chrome profile\n  setupProfile: async (\n    request: SetupProfileRequest,\n    apiKey: string\n  ): Promise<{ status: string; message: string }> => {\n    const response = await api.post('/admin/setup-chrome-profile/', request, {\n      headers: { 'X-API-KEY': apiKey },\n    });\n    return response.data;\n  },\n\n  // List profiles\n  listProfiles: async (apiKey: string): Promise<string[]> => {\n    const response = await api.get('/admin/list-profiles/', {\n      headers: { 'X-API-KEY': apiKey },\n    });\n    return response.data;\n  },\n\n  // Delete profile\n  deleteProfile: async (profileName: string, apiKey: string): Promise<{ status: string }> => {\n    const response = await api.delete(`/admin/delete-profile/${profileName}`, {\n      headers: { 'X-API-KEY': apiKey },\n    });\n    return response.data;\n  },\n};\n"], "names": [], "mappings": ";;;;;AAGqB;AAHrB;;AAGA,MAAM,eAAe,6DAA2B;AAEzC,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,SAAS;IACT,SAAS;AACX;AAGO,MAAM,WAAW;IACtB,aAAa;IACb,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,eAAe;QAC/C,OAAO,SAAS,IAAI;IACtB;IAEA,iEAAiE;IACjE,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,wBAAwB;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,oCAAoC;IACpC,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,8BAA8B;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAEO,MAAM,WAAW;IACtB,uBAAuB;IACvB,cAAc,OACZ,SACA;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,gCAAgC,SAAS;YACvE,SAAS;gBAAE,aAAa;YAAO;QACjC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,yBAAyB;YACtD,SAAS;gBAAE,aAAa;YAAO;QACjC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,eAAe,OAAO,aAAqB;QACzC,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,sBAAsB,EAAE,aAAa,EAAE;YACxE,SAAS;gBAAE,aAAa;YAAO;QACjC;QACA,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/stores/crawler-store.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { crawlApi } from '@/lib/api';\nimport { CrawlRequest, CrawledData, ChatWithManusRequest } from '@/lib/types';\n\ninterface CrawlerState {\n  results: CrawledData | null;\n  isLoading: boolean;\n  error: string | null;\n  status: string | null;\n  progress: number | undefined;\n  currentRequestId: string | null;\n\n  // Actions\n  startCrawl: (request: CrawlRequest) => Promise<void>;\n  startChat: (request: ChatWithManusRequest) => Promise<void>;\n  setResults: (results: CrawledData) => void;\n  setStatus: (status: string) => void;\n  setProgress: (progress: number) => void;\n  setError: (error: string) => void;\n  clearError: () => void;\n  reset: () => void;\n}\n\nexport const useCrawlerStore = create<CrawlerState>((set, get) => ({\n  results: null,\n  isLoading: false,\n  error: null,\n  status: null,\n  progress: undefined,\n  currentRequestId: null,\n\n  startCrawl: async (request: CrawlRequest) => {\n    set({ \n      isLoading: true, \n      error: null, \n      status: 'Starting crawl...', \n      progress: 0,\n      currentRequestId: request.request_id \n    });\n    \n    try {\n      await crawlApi.crawlUrlRealtime(request);\n      set({ status: 'Crawl started successfully' });\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to start crawl',\n        isLoading: false,\n        status: null,\n        progress: undefined,\n      });\n    }\n  },\n\n  startChat: async (request: ChatWithManusRequest) => {\n    set({ \n      isLoading: true, \n      error: null, \n      status: 'Starting chat...', \n      progress: 0,\n      currentRequestId: request.request_id \n    });\n    \n    try {\n      await crawlApi.chatWithManusRealtime(request);\n      set({ status: 'Chat started successfully' });\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to start chat',\n        isLoading: false,\n        status: null,\n        progress: undefined,\n      });\n    }\n  },\n\n  setResults: (results: CrawledData) => {\n    set({ results, isLoading: false, progress: 100 });\n  },\n\n  setStatus: (status: string) => {\n    set({ status });\n  },\n\n  setProgress: (progress: number) => {\n    set({ progress });\n  },\n\n  setError: (error: string) => {\n    set({ error, isLoading: false, status: null, progress: undefined });\n  },\n\n  clearError: () => {\n    set({ error: null });\n  },\n\n  reset: () => {\n    set({\n      results: null,\n      isLoading: false,\n      error: null,\n      status: null,\n      progress: undefined,\n      currentRequestId: null,\n    });\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAsBO,MAAM,kBAAkB,CAAA,GAAA,2JAAA,CAAA,SAAM,AAAD,EAAgB,CAAC,KAAK,MAAQ,CAAC;QACjE,SAAS;QACT,WAAW;QACX,OAAO;QACP,QAAQ;QACR,UAAU;QACV,kBAAkB;QAElB,YAAY,OAAO;YACjB,IAAI;gBACF,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,kBAAkB,QAAQ,UAAU;YACtC;YAEA,IAAI;gBACF,MAAM,oHAAA,CAAA,WAAQ,CAAC,gBAAgB,CAAC;gBAChC,IAAI;oBAAE,QAAQ;gBAA6B;YAC7C,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;oBACX,QAAQ;oBACR,UAAU;gBACZ;YACF;QACF;QAEA,WAAW,OAAO;YAChB,IAAI;gBACF,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,kBAAkB,QAAQ,UAAU;YACtC;YAEA,IAAI;gBACF,MAAM,oHAAA,CAAA,WAAQ,CAAC,qBAAqB,CAAC;gBACrC,IAAI;oBAAE,QAAQ;gBAA4B;YAC5C,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;oBACX,QAAQ;oBACR,UAAU;gBACZ;YACF;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;gBAAS,WAAW;gBAAO,UAAU;YAAI;QACjD;QAEA,WAAW,CAAC;YACV,IAAI;gBAAE;YAAO;QACf;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;QACjB;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;gBAAO,WAAW;gBAAO,QAAQ;gBAAM,UAAU;YAAU;QACnE;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,OAAO;YACL,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,kBAAkB;YACpB;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1383, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/stores/admin-store.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { adminApi } from '@/lib/api';\nimport { SetupProfileRequest } from '@/lib/types';\n\ninterface AdminState {\n  apiKey: string;\n  profiles: string[];\n  isLoading: boolean;\n  error: string | null;\n\n  // Actions\n  setApiKey: (key: string) => void;\n  loadProfiles: () => Promise<void>;\n  setupProfile: (request: SetupProfileRequest) => Promise<boolean>;\n  deleteProfile: (profileName: string) => Promise<boolean>;\n  clearError: () => void;\n}\n\nexport const useAdminStore = create<AdminState>((set, get) => ({\n  apiKey: '',\n  profiles: [],\n  isLoading: false,\n  error: null,\n\n  setApiKey: (key: string) => {\n    set({ apiKey: key });\n  },\n\n  loadProfiles: async () => {\n    const { apiKey } = get();\n    if (!apiKey) {\n      set({ error: 'API Key is required' });\n      return;\n    }\n\n    set({ isLoading: true, error: null });\n    try {\n      const profiles = await adminApi.listProfiles(apiKey);\n      set({ profiles, isLoading: false });\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to load profiles',\n        isLoading: false\n      });\n    }\n  },\n\n  setupProfile: async (request: SetupProfileRequest) => {\n    const { apiKey } = get();\n    if (!apiKey) {\n      set({ error: 'API Key is required' });\n      return false;\n    }\n\n    set({ isLoading: true, error: null });\n    try {\n      await adminApi.setupProfile(request, apiKey);\n      await get().loadProfiles(); // Reload profiles\n      set({ isLoading: false });\n      return true;\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to setup profile',\n        isLoading: false\n      });\n      return false;\n    }\n  },\n\n  deleteProfile: async (profileName: string) => {\n    const { apiKey } = get();\n    if (!apiKey) {\n      set({ error: 'API Key is required' });\n      return false;\n    }\n\n    set({ isLoading: true, error: null });\n    try {\n      await adminApi.deleteProfile(profileName, apiKey);\n      await get().loadProfiles(); // Reload profiles\n      set({ isLoading: false });\n      return true;\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to delete profile',\n        isLoading: false\n      });\n      return false;\n    }\n  },\n\n  clearError: () => {\n    set({ error: null });\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAiBO,MAAM,gBAAgB,CAAA,GAAA,2JAAA,CAAA,SAAM,AAAD,EAAc,CAAC,KAAK,MAAQ,CAAC;QAC7D,QAAQ;QACR,UAAU,EAAE;QACZ,WAAW;QACX,OAAO;QAEP,WAAW,CAAC;YACV,IAAI;gBAAE,QAAQ;YAAI;QACpB;QAEA,cAAc;YACZ,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,CAAC,QAAQ;gBACX,IAAI;oBAAE,OAAO;gBAAsB;gBACnC;YACF;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC;gBAC7C,IAAI;oBAAE;oBAAU,WAAW;gBAAM;YACnC,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;YACF;QACF;QAEA,cAAc,OAAO;YACnB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,CAAC,QAAQ;gBACX,IAAI;oBAAE,OAAO;gBAAsB;gBACnC,OAAO;YACT;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,oHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS;gBACrC,MAAM,MAAM,YAAY,IAAI,kBAAkB;gBAC9C,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,OAAO;YACT;QACF;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,CAAC,QAAQ;gBACX,IAAI;oBAAE,OAAO;gBAAsB;gBACnC,OAAO;YACT;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,oHAAA,CAAA,WAAQ,CAAC,aAAa,CAAC,aAAa;gBAC1C,MAAM,MAAM,YAAY,IAAI,kBAAkB;gBAC9C,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,OAAO;YACT;QACF;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1494, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/crawler/crawler-form.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { useCrawlerStore } from '@/stores/crawler-store';\nimport { useAdminStore } from '@/stores/admin-store';\nimport { Bug, Settings } from 'lucide-react';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface CrawlerFormProps {\n  onStartCrawl?: () => void;\n}\n\nexport function CrawlerForm({ onStartCrawl }: CrawlerFormProps) {\n  const [url, setUrl] = useState('https://manus.im/');\n  const [selectedProfile, setSelectedProfile] = useState<string>('');\n  const [headless, setHeadless] = useState(true);\n\n  const { startCrawl, isLoading } = useCrawlerStore();\n  const { profiles } = useAdminStore();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!url.trim()) return;\n\n    const requestId = uuidv4();\n\n    await startCrawl({\n      url: url.trim(),\n      profile_name: selectedProfile || undefined,\n      headless,\n      request_id: requestId\n    });\n\n    onStartCrawl?.();\n  };\n\n  return (\n    <Card className=\"w-full max-w-2xl\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center space-x-2\">\n          <Bug className=\"h-5 w-5\" />\n          <span>Start Crawling</span>\n        </CardTitle>\n        <CardDescription>\n          Configure and start crawling a Manus.im page\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* URL Input */}\n          <div>\n            <label className=\"text-sm font-medium text-text-primary mb-2 block\">\n              Target URL\n            </label>\n            <Input\n              type=\"url\"\n              placeholder=\"https://manus.im/\"\n              value={url}\n              onChange={(e) => setUrl(e.target.value)}\n              required\n            />\n          </div>\n\n          {/* Profile Selection */}\n          <div>\n            <label className=\"text-sm font-medium text-text-primary mb-2 block\">\n              Chrome Profile (Optional)\n            </label>\n            <Select value={selectedProfile} onValueChange={setSelectedProfile}>\n              <SelectTrigger>\n                <SelectValue placeholder=\"Select a profile or leave empty for default\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"\">Default (No Profile)</SelectItem>\n                {profiles.map((profile) => (\n                  <SelectItem key={profile} value={profile}>\n                    {profile}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Headless Mode */}\n          <div className=\"flex items-center space-x-2\">\n            <Checkbox\n              id=\"headless\"\n              checked={headless}\n              onCheckedChange={(checked) => setHeadless(checked as boolean)}\n            />\n            <label\n              htmlFor=\"headless\"\n              className=\"text-sm font-medium text-text-primary cursor-pointer\"\n            >\n              Run in headless mode (recommended)\n            </label>\n          </div>\n\n          {/* Submit Button */}\n          <Button\n            type=\"submit\"\n            disabled={isLoading || !url.trim()}\n            className=\"w-full\"\n          >\n            {isLoading ? (\n              <>\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                Crawling...\n              </>\n            ) : (\n              <>\n                <Bug className=\"h-4 w-4 mr-2\" />\n                Start Crawling\n              </>\n            )}\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAiBO,SAAS,YAAY,EAAE,YAAY,EAAoB;;IAC5D,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAChD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;IAEjC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,IAAI,IAAI,IAAI;QAEjB,MAAM,YAAY,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QAEvB,MAAM,WAAW;YACf,KAAK,IAAI,IAAI;YACb,cAAc,mBAAmB;YACjC;YACA,YAAY;QACd;QAEA;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAmD;;;;;;8CAGpE,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;oCACtC,QAAQ;;;;;;;;;;;;sCAKZ,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAmD;;;;;;8CAGpE,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAiB,eAAe;;sDAC7C,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAG;;;;;;gDACpB,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,qIAAA,CAAA,aAAU;wDAAe,OAAO;kEAC9B;uDADc;;;;;;;;;;;;;;;;;;;;;;;sCASzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,SAAS;oCACT,iBAAiB,CAAC,UAAY,YAAY;;;;;;8CAE5C,6LAAC;oCACC,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,UAAU,aAAa,CAAC,IAAI,IAAI;4BAChC,WAAU;sCAET,0BACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAAuE;;6DAIxF;;kDACE,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;GA7GgB;;QAKoB,oIAAA,CAAA,kBAAe;QAC5B,kIAAA,CAAA,gBAAa;;;KANpB", "debugId": null}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/lib/websocket.ts"], "sourcesContent": ["import { WebSocketMessage } from './types';\n\nexport class WebSocketManager {\n  private ws: WebSocket | null = null;\n  private url: string;\n  private requestId: string;\n  private onMessage: (message: WebSocketMessage) => void;\n  private onError: (error: Event) => void;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n\n  constructor(\n    requestId: string,\n    onMessage: (message: WebSocketMessage) => void,\n    onError: (error: Event) => void = () => {}\n  ) {\n    this.requestId = requestId;\n    this.onMessage = onMessage;\n    this.onError = onError;\n    this.url = `${process.env.WS_URL || 'ws://localhost:8000'}/ws/crawl-status/${requestId}`;\n  }\n\n  connect(): void {\n    try {\n      this.ws = new WebSocket(this.url);\n\n      this.ws.onopen = () => {\n        console.log(`WebSocket connected for request: ${this.requestId}`);\n        this.reconnectAttempts = 0;\n      };\n\n      this.ws.onmessage = (event) => {\n        try {\n          const message: WebSocketMessage = JSON.parse(event.data);\n          this.onMessage(message);\n        } catch (error) {\n          console.error('Failed to parse WebSocket message:', error);\n        }\n      };\n\n      this.ws.onclose = () => {\n        console.log('WebSocket connection closed');\n        this.attemptReconnect();\n      };\n\n      this.ws.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        this.onError(error);\n      };\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      this.onError(error as Event);\n    }\n  }\n\n  private attemptReconnect(): void {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n\n      setTimeout(() => {\n        this.connect();\n      }, 1000 * this.reconnectAttempts);\n    }\n  }\n\n  disconnect(): void {\n    if (this.ws) {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n\n  isConnected(): boolean {\n    return this.ws?.readyState === WebSocket.OPEN;\n  }\n}\n"], "names": [], "mappings": ";;;AAmBkB;AAjBX,MAAM;IACH,KAAuB,KAAK;IAC5B,IAAY;IACZ,UAAkB;IAClB,UAA+C;IAC/C,QAAgC;IAChC,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;IAEjC,YACE,SAAiB,EACjB,SAA8C,EAC9C,UAAkC,KAAO,CAAC,CAC1C;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,GAAG,GAAG,GAAG,2DAAsB,sBAAsB,iBAAiB,EAAE,WAAW;IAC1F;IAEA,UAAgB;QACd,IAAI;YACF,IAAI,CAAC,EAAE,GAAG,IAAI,UAAU,IAAI,CAAC,GAAG;YAEhC,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG;gBACf,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,SAAS,EAAE;gBAChE,IAAI,CAAC,iBAAiB,GAAG;YAC3B;YAEA,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;gBACnB,IAAI;oBACF,MAAM,UAA4B,KAAK,KAAK,CAAC,MAAM,IAAI;oBACvD,IAAI,CAAC,SAAS,CAAC;gBACjB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACtD;YACF;YAEA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG;gBAChB,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,gBAAgB;YACvB;YAEA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC;gBACjB,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,IAAI,CAAC,OAAO,CAAC;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,IAAI,CAAC,OAAO,CAAC;QACf;IACF;IAEQ,mBAAyB;QAC/B,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACtD,IAAI,CAAC,iBAAiB;YACtB,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;YAEjG,WAAW;gBACT,IAAI,CAAC,OAAO;YACd,GAAG,OAAO,IAAI,CAAC,iBAAiB;QAClC;IACF;IAEA,aAAmB;QACjB,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,IAAI,CAAC,EAAE,CAAC,KAAK;YACb,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;IAEA,cAAuB;QACrB,OAAO,IAAI,CAAC,EAAE,EAAE,eAAe,UAAU,IAAI;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 1845, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/hooks/use-websocket.ts"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\nimport { WebSocketManager } from '@/lib/websocket';\nimport { WebSocketMessage } from '@/lib/types';\n\nexport function useWebSocket(requestId: string | null) {\n  const [isConnected, setIsConnected] = useState(false);\n  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  const wsManager = useRef<WebSocketManager | null>(null);\n\n  useEffect(() => {\n    if (!requestId) return;\n\n    const handleMessage = (message: WebSocketMessage) => {\n      setLastMessage(message);\n    };\n\n    const handleError = (error: Event) => {\n      setError('WebSocket connection error');\n      setIsConnected(false);\n    };\n\n    wsManager.current = new WebSocketManager(requestId, handleMessage, handleError);\n    wsManager.current.connect();\n\n    const checkConnection = setInterval(() => {\n      if (wsManager.current) {\n        setIsConnected(wsManager.current.isConnected());\n      }\n    }, 1000);\n\n    return () => {\n      clearInterval(checkConnection);\n      if (wsManager.current) {\n        wsManager.current.disconnect();\n        wsManager.current = null;\n      }\n    };\n  }, [requestId]);\n\n  return {\n    isConnected,\n    lastMessage,\n    error,\n    clearError: () => setError(null),\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAGO,SAAS,aAAa,SAAwB;;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA2B;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,WAAW;YAEhB,MAAM;wDAAgB,CAAC;oBACrB,eAAe;gBACjB;;YAEA,MAAM;sDAAc,CAAC;oBACnB,SAAS;oBACT,eAAe;gBACjB;;YAEA,UAAU,OAAO,GAAG,IAAI,0HAAA,CAAA,mBAAgB,CAAC,WAAW,eAAe;YACnE,UAAU,OAAO,CAAC,OAAO;YAEzB,MAAM,kBAAkB;0DAAY;oBAClC,IAAI,UAAU,OAAO,EAAE;wBACrB,eAAe,UAAU,OAAO,CAAC,WAAW;oBAC9C;gBACF;yDAAG;YAEH;0CAAO;oBACL,cAAc;oBACd,IAAI,UAAU,OAAO,EAAE;wBACrB,UAAU,OAAO,CAAC,UAAU;wBAC5B,UAAU,OAAO,GAAG;oBACtB;gBACF;;QACF;iCAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA,YAAY,IAAM,SAAS;IAC7B;AACF;GA1CgB", "debugId": null}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/app/crawler/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ManusStyleSidebar } from '@/components/manus/sidebar';\nimport { ManusStyleMainContent } from '@/components/manus/main-content';\nimport { CrawlerForm } from '@/components/crawler/crawler-form';\nimport { useCrawlerStore } from '@/stores/crawler-store';\nimport { useAdminStore } from '@/stores/admin-store';\nimport { useWebSocket } from '@/hooks/use-websocket';\nimport { TaskItem, ChatMessage, CrawledData, InteractiveChatResult } from '@/lib/types';\nimport { v4 as uuidv4 } from 'uuid';\n\nexport default function CrawlerPage() {\n  const {\n    results,\n    isLoading,\n    currentRequestId,\n    setResults,\n    setStatus,\n    setError,\n    startChat\n  } = useCrawlerStore();\n\n  const { loadProfiles } = useAdminStore();\n  const [selectedTask, setSelectedTask] = useState<TaskItem | null>(null);\n  const [showForm, setShowForm] = useState(true);\n\n  // WebSocket connection\n  const { lastMessage, isConnected } = useWebSocket(currentRequestId);\n\n  // Load profiles on mount\n  useEffect(() => {\n    loadProfiles();\n  }, [loadProfiles]);\n\n  // Handle WebSocket messages\n  useEffect(() => {\n    if (lastMessage) {\n      switch (lastMessage.type) {\n        case 'progress':\n          setStatus(lastMessage.message);\n          break;\n        case 'data':\n          if (lastMessage.data) {\n            // Check if it's crawled data or chat result\n            if ('chat_response' in lastMessage.data) {\n              // It's an interactive chat result\n              const chatResult = lastMessage.data as InteractiveChatResult;\n              if (chatResult.updated_page_data) {\n                setResults(chatResult.updated_page_data);\n              }\n            } else {\n              // It's regular crawled data\n              setResults(lastMessage.data as CrawledData);\n              setShowForm(false); // Hide form after successful crawl\n            }\n          }\n          break;\n        case 'error':\n          setError(lastMessage.message);\n          break;\n      }\n    }\n  }, [lastMessage, setResults, setStatus, setError]);\n\n  // Convert crawled data to display format\n  const tasks: TaskItem[] = results?.tasks || [];\n  const messages: ChatMessage[] = results?.chat_messages || [];\n\n  const handleTaskSelect = (task: TaskItem) => {\n    setSelectedTask(task);\n  };\n\n  const handleNewTask = () => {\n    setShowForm(true);\n    setSelectedTask(null);\n  };\n\n  const handleStartCrawl = () => {\n    setShowForm(false);\n  };\n\n  const handleSendMessage = async (message: string) => {\n    if (!results?.page_title) return;\n\n    const requestId = uuidv4();\n\n    await startChat({\n      message,\n      task_url: window.location.href, // Current page URL\n      profile_name: undefined, // Could be made configurable\n      request_id: requestId,\n      headless: true\n    });\n  };\n\n  return (\n    <div className=\"flex w-full h-screen overflow-hidden\">\n      {/* Sidebar */}\n      <ManusStyleSidebar\n        tasks={tasks}\n        onTaskSelect={handleTaskSelect}\n        onNewTask={handleNewTask}\n      />\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col\">\n        {showForm ? (\n          <div className=\"flex-1 flex items-center justify-center p-6\">\n            <CrawlerForm onStartCrawl={handleStartCrawl} />\n          </div>\n        ) : (\n          <ManusStyleMainContent\n            title={selectedTask?.title || results?.page_title || \"Manus Crawler\"}\n            messages={messages}\n            onSendMessage={handleSendMessage}\n            isLoading={isLoading}\n          />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAVA;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,EACJ,OAAO,EACP,SAAS,EACT,gBAAgB,EAChB,UAAU,EACV,SAAS,EACT,QAAQ,EACR,SAAS,EACV,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,uBAAuB;IACvB,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;IAElD,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;KAAa;IAEjB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,aAAa;gBACf,OAAQ,YAAY,IAAI;oBACtB,KAAK;wBACH,UAAU,YAAY,OAAO;wBAC7B;oBACF,KAAK;wBACH,IAAI,YAAY,IAAI,EAAE;4BACpB,4CAA4C;4BAC5C,IAAI,mBAAmB,YAAY,IAAI,EAAE;gCACvC,kCAAkC;gCAClC,MAAM,aAAa,YAAY,IAAI;gCACnC,IAAI,WAAW,iBAAiB,EAAE;oCAChC,WAAW,WAAW,iBAAiB;gCACzC;4BACF,OAAO;gCACL,4BAA4B;gCAC5B,WAAW,YAAY,IAAI;gCAC3B,YAAY,QAAQ,mCAAmC;4BACzD;wBACF;wBACA;oBACF,KAAK;wBACH,SAAS,YAAY,OAAO;wBAC5B;gBACJ;YACF;QACF;gCAAG;QAAC;QAAa;QAAY;QAAW;KAAS;IAEjD,yCAAyC;IACzC,MAAM,QAAoB,SAAS,SAAS,EAAE;IAC9C,MAAM,WAA0B,SAAS,iBAAiB,EAAE;IAE5D,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,gBAAgB;QACpB,YAAY;QACZ,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,YAAY;IACd;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,SAAS,YAAY;QAE1B,MAAM,YAAY,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QAEvB,MAAM,UAAU;YACd;YACA,UAAU,OAAO,QAAQ,CAAC,IAAI;YAC9B,cAAc;YACd,YAAY;YACZ,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,yIAAA,CAAA,oBAAiB;gBAChB,OAAO;gBACP,cAAc;gBACd,WAAW;;;;;;0BAIb,6LAAC;gBAAI,WAAU;0BACZ,yBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mJAAA,CAAA,cAAW;wBAAC,cAAc;;;;;;;;;;yCAG7B,6LAAC,iJAAA,CAAA,wBAAqB;oBACpB,OAAO,cAAc,SAAS,SAAS,cAAc;oBACrD,UAAU;oBACV,eAAe;oBACf,WAAW;;;;;;;;;;;;;;;;;AAMvB;GA9GwB;;QASlB,oIAAA,CAAA,kBAAe;QAEM,kIAAA,CAAA,gBAAa;QAKD,mIAAA,CAAA,eAAY;;;KAhB3B", "debugId": null}}]}