{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { Button } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Bug, Settings, Home } from \"lucide-react\";\n\ninterface FeatureItemProps {\n  icon: string;\n  title: string;\n  description: string;\n}\n\nfunction FeatureItem({ icon, title, description }: FeatureItemProps) {\n  return (\n    <div className=\"group p-4 rounded-xl bg-fill-tsp-white-dark/50 hover:bg-fill-tsp-white-dark border border-border-light hover:border-primary/20 transition-all duration-300 hover:scale-105\">\n      <div className=\"flex items-start space-x-3\">\n        <div className=\"text-2xl group-hover:scale-110 transition-transform duration-300\">\n          {icon}\n        </div>\n        <div className=\"flex-1\">\n          <h3 className=\"font-semibold text-text-primary group-hover:text-primary transition-colors duration-300\">\n            {title}\n          </h3>\n          <p className=\"text-sm text-text-tertiary mt-1 leading-relaxed\">\n            {description}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function HomePage() {\n  return (\n    <div className=\"flex-1 flex items-center justify-center p-6 bg-gradient-to-br from-background via-background to-background-menu-white\">\n      <div className=\"max-w-6xl w-full space-y-12\">\n        {/* Header */}\n        <div className=\"text-center space-y-6 animate-in fade-in-0 duration-1000\">\n          <div className=\"flex items-center justify-center space-x-3 group\">\n            <div className=\"relative\">\n              <Bug className=\"h-12 w-12 text-primary transition-transform group-hover:scale-110 duration-300\" />\n              <div className=\"absolute inset-0 h-12 w-12 bg-primary/20 rounded-full blur-xl animate-pulse\"></div>\n            </div>\n            <h1 className=\"text-5xl font-bold bg-gradient-to-r from-text-primary to-text-secondary bg-clip-text text-transparent\">\n              Manus Crawler\n            </h1>\n          </div>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed\">\n            Modern NextJS frontend for Manus.im crawler with realtime updates, interactive chat,\n            and comprehensive Chrome profile management\n          </p>\n          <div className=\"flex items-center justify-center space-x-2 text-sm text-text-tertiary\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            <span>System Online</span>\n          </div>\n        </div>\n\n        {/* Feature Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 animate-in slide-in-from-bottom-4 duration-1000 delay-300\">\n          <Card className=\"group relative overflow-hidden border-border-main bg-gradient-to-br from-fill-tsp-white-main to-fill-tsp-white-dark hover:from-fill-tsp-white-dark hover:to-fill-tsp-white-main transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-primary/10\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n            <CardHeader className=\"relative z-10\">\n              <CardTitle className=\"flex items-center space-x-3 text-lg\">\n                <div className=\"p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300\">\n                  <Bug className=\"h-6 w-6 text-primary group-hover:scale-110 transition-transform duration-300\" />\n                </div>\n                <span className=\"group-hover:text-primary transition-colors duration-300\">Crawler Interface</span>\n              </CardTitle>\n              <CardDescription className=\"text-text-tertiary leading-relaxed\">\n                Crawl Manus.im pages with realtime updates, WebSocket integration, and interactive chat capabilities\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"relative z-10\">\n              <Button asChild className=\"w-full group-hover:bg-primary/90 transition-all duration-300 shadow-lg hover:shadow-xl\">\n                <Link href=\"/crawler\">\n                  <span className=\"flex items-center justify-center space-x-2\">\n                    <span>Start Crawling</span>\n                    <Bug className=\"h-4 w-4 transition-transform group-hover:translate-x-1\" />\n                  </span>\n                </Link>\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card className=\"group relative overflow-hidden border-border-main bg-gradient-to-br from-fill-tsp-white-main to-fill-tsp-white-dark hover:from-fill-tsp-white-dark hover:to-fill-tsp-white-main transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-secondary/10\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-secondary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n            <CardHeader className=\"relative z-10\">\n              <CardTitle className=\"flex items-center space-x-3 text-lg\">\n                <div className=\"p-2 rounded-lg bg-secondary/10 group-hover:bg-secondary/20 transition-colors duration-300\">\n                  <Settings className=\"h-6 w-6 text-secondary group-hover:scale-110 transition-transform duration-300\" />\n                </div>\n                <span className=\"group-hover:text-secondary transition-colors duration-300\">Admin Dashboard</span>\n              </CardTitle>\n              <CardDescription className=\"text-text-tertiary leading-relaxed\">\n                Manage Chrome profiles, configure crawler settings, and monitor system performance\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"relative z-10\">\n              <Button asChild variant=\"outline\" className=\"w-full border-secondary/20 hover:border-secondary hover:bg-secondary/10 transition-all duration-300 shadow-lg hover:shadow-xl\">\n                <Link href=\"/admin\">\n                  <span className=\"flex items-center justify-center space-x-2\">\n                    <span>Admin Panel</span>\n                    <Settings className=\"h-4 w-4 transition-transform group-hover:rotate-90\" />\n                  </span>\n                </Link>\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Features List */}\n        <div className=\"relative overflow-hidden bg-gradient-to-br from-fill-tsp-white-main via-fill-tsp-white-dark to-fill-tsp-white-main rounded-2xl p-8 border border-border-light animate-in slide-in-from-bottom-6 duration-1000 delay-500\">\n          <div className=\"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 opacity-50\"></div>\n          <div className=\"relative z-10\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-3xl font-bold text-text-primary mb-2\">Powerful Features</h2>\n              <p className=\"text-text-secondary\">Built with modern technologies for optimal performance</p>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              <FeatureItem\n                icon=\"⚡\"\n                title=\"NextJS 14+ App Router\"\n                description=\"Modern React framework with TypeScript support\"\n              />\n              <FeatureItem\n                icon=\"🔄\"\n                title=\"Realtime WebSocket\"\n                description=\"Live updates and progress tracking\"\n              />\n              <FeatureItem\n                icon=\"🎨\"\n                title=\"Manus-style UI\"\n                description=\"Dark theme matching Manus.im aesthetic\"\n              />\n              <FeatureItem\n                icon=\"💬\"\n                title=\"Interactive Chat\"\n                description=\"Communicate with Manus during crawling\"\n              />\n              <FeatureItem\n                icon=\"🔧\"\n                title=\"Profile Management\"\n                description=\"Chrome profiles and settings control\"\n              />\n              <FeatureItem\n                icon=\"🛡️\"\n                title=\"Type-safe API\"\n                description=\"Zod validation and error handling\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;;;;;;AAQA,SAAS,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAoB;IACjE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX;;;;;;sCAEH,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAMb;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAG,WAAU;8CAAwG;;;;;;;;;;;;sCAIxH,8OAAC;4BAAE,WAAU;sCAAgE;;;;;;sCAI7E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;8BAKV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,8OAAC;oDAAK,WAAU;8DAA0D;;;;;;;;;;;;sDAE5E,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAqC;;;;;;;;;;;;8CAIlE,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,WAAU;kDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;kEAAK;;;;;;kEACN,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOzB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAK,WAAU;8DAA4D;;;;;;;;;;;;sDAE9E,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAqC;;;;;;;;;;;;8CAIlE,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAU,WAAU;kDAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;kEAAK;;;;;;kEACN,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAShC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAAsB;;;;;;;;;;;;8CAErC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;sDAEd,8OAAC;4CACC,MAAK;4CACL,OAAM;4CACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B", "debugId": null}}]}