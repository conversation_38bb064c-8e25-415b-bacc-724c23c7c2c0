{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/manus/sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { TaskItem } from '@/lib/types';\nimport { PanelRight, Search, Plus, Command, MoreHorizontal } from 'lucide-react';\n\ninterface SidebarProps {\n  tasks: TaskItem[];\n  onTaskSelect?: (task: TaskItem) => void;\n  onNewTask?: () => void;\n}\n\nexport function ManusStyleSidebar({ tasks, onTaskSelect, onNewTask }: SidebarProps) {\n  const [isCollapsed, setIsCollapsed] = useState(false);\n\n  return (\n    <div\n      className=\"h-full flex flex-col bg-gradient-to-b from-background-nav to-background-menu-white border-r border-border-main shadow-xl manus-transition\"\n      style={{\n        width: isCollapsed ? '0px' : '320px',\n        transition: 'width 0.28s cubic-bezier(0.4, 0, 0.2, 1)'\n      }}\n    >\n      {/* Header */}\n      <div className=\"flex\">\n        <div className=\"flex items-center px-3 py-3 flex-row h-[52px] gap-1 justify-end w-full\">\n          <div className=\"flex justify-between w-full px-1 pt-2\">\n            <div className=\"relative flex\">\n              <button\n                onClick={() => setIsCollapsed(!isCollapsed)}\n                className=\"flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-fill-tsp-gray-main rounded-md manus-transition\"\n              >\n                <PanelRight className=\"h-5 w-5 text-icon-secondary\" />\n              </button>\n            </div>\n            <div className=\"flex flex-row gap-1\">\n              <button className=\"flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-fill-tsp-gray-main rounded-md manus-transition\">\n                <Search className=\"h-5 w-5 text-icon-secondary\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* New Task Button */}\n      <div className=\"px-4 mb-4 flex justify-center flex-shrink-0\">\n        <button\n          onClick={onNewTask}\n          className=\"group flex min-w-[36px] w-full items-center justify-center gap-2 rounded-xl h-[40px] bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-white cursor-pointer shadow-lg hover:shadow-xl manus-transition hover:scale-105\"\n        >\n          <Plus className=\"h-5 w-5 group-hover:rotate-90 transition-transform duration-300\" />\n          <span className=\"text-sm font-medium whitespace-nowrap truncate\">\n            New Task\n          </span>\n          <div className=\"flex items-center gap-1 opacity-70 group-hover:opacity-100 transition-opacity\">\n            <span className=\"flex justify-center items-center min-w-5 h-5 px-1 rounded bg-white/20 border border-white/30\">\n              <Command className=\"h-3 w-3\" />\n            </span>\n            <span className=\"flex justify-center items-center w-5 h-5 px-1 rounded bg-white/20 border border-white/30 text-xs font-medium\">\n              K\n            </span>\n          </div>\n        </button>\n      </div>\n\n      {/* Tasks List */}\n      <ScrollArea className=\"flex-1 px-2 pb-5 overflow-x-hidden\">\n        <div className=\"space-y-1\">\n          {tasks.map((task, index) => (\n            <TaskItemComponent\n              key={index}\n              task={task}\n              onClick={() => onTaskSelect?.(task)}\n            />\n          ))}\n        </div>\n      </ScrollArea>\n    </div>\n  );\n}\n\ninterface TaskItemComponentProps {\n  task: TaskItem;\n  onClick?: () => void;\n}\n\nfunction TaskItemComponent({ task, onClick }: TaskItemComponentProps) {\n  return (\n    <div\n      onClick={onClick}\n      className=\"group flex h-16 cursor-pointer items-center gap-3 rounded-xl px-3 py-2 manus-transition hover:bg-gradient-to-r hover:from-fill-tsp-gray-main hover:to-transparent border border-transparent hover:border-border-light hover:shadow-md\"\n    >\n      {/* Task Icon */}\n      <div className=\"relative flex-shrink-0\">\n        <div className=\"h-10 w-10 rounded-xl flex items-center justify-center relative bg-gradient-to-br from-fill-tsp-white-dark to-fill-tsp-white-main group-hover:from-primary/20 group-hover:to-primary/10 transition-all duration-300 border border-border-light group-hover:border-primary/30\">\n          <div className=\"relative h-5 w-5 object-cover brightness-0 opacity-75 dark:opacity-100 dark:brightness-100 group-hover:scale-110 transition-transform duration-300\">\n            {task.icon_src ? (\n              <img\n                alt={task.title || 'Task'}\n                className=\"w-full h-full object-cover rounded\"\n                src={task.icon_src}\n              />\n            ) : (\n              <div className=\"w-full h-full bg-gradient-to-br from-primary/30 to-secondary/30 rounded\" />\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Task Content */}\n      <div className=\"min-w-20 flex-1 manus-transition opacity-100\">\n        <div className=\"flex items-center gap-1 overflow-x-hidden\">\n          <span\n            className=\"truncate text-sm font-medium text-text-primary flex-1 min-w-0\"\n            title={task.title}\n          >\n            {task.title || 'Untitled Task'}\n          </span>\n          <span className=\"text-text-tertiary text-xs whitespace-nowrap\">\n            {task.timestamp}\n          </span>\n        </div>\n        <div className=\"flex items-center gap-2 h-[18px] relative\">\n          <span\n            className=\"min-w-0 flex-1 truncate text-xs text-text-tertiary\"\n            title={task.preview || task.title}\n          >\n            {task.preview || task.title || 'No preview available'}\n          </span>\n          <div className=\"w-[22px] h-[22px] flex rounded-[6px] items-center justify-center pointer invisible cursor-pointer bg-background-menu-white border border-border-main shadow-sm group-hover:visible touch-device:visible\">\n            <MoreHorizontal className=\"h-4 w-4 text-icon-secondary\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;AAcO,SAAS,kBAAkB,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAgB;IAChF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,OAAO,cAAc,QAAQ;YAC7B,YAAY;QACd;;0BAGA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;8CAEV,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAG1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAiD;;;;;;sCAGjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CACd,cAAA,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,8OAAC;oCAAK,WAAU;8CAA+G;;;;;;;;;;;;;;;;;;;;;;;0BAQrI,8OAAC,0IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAEC,MAAM;4BACN,SAAS,IAAM,eAAe;2BAFzB;;;;;;;;;;;;;;;;;;;;;AASnB;AAOA,SAAS,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAA0B;IAClE,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,KAAK,QAAQ,iBACZ,8OAAC;4BACC,KAAK,KAAK,KAAK,IAAI;4BACnB,WAAU;4BACV,KAAK,KAAK,QAAQ;;;;;iDAGpB,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO,KAAK,KAAK;0CAEhB,KAAK,KAAK,IAAI;;;;;;0CAEjB,8OAAC;gCAAK,WAAU;0CACb,KAAK,SAAS;;;;;;;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO,KAAK,OAAO,IAAI,KAAK,KAAK;0CAEhC,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/manus/main-content.tsx"], "sourcesContent": ["'use client';\n\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { ChatMessage } from '@/lib/types';\nimport { Send, Paperclip } from 'lucide-react';\nimport { useState } from 'react';\n\ninterface MainContentProps {\n  title?: string;\n  messages: ChatMessage[];\n  onSendMessage?: (message: string) => void;\n  isLoading?: boolean;\n}\n\nexport function ManusStyleMainContent({\n  title = \"Manus Crawler\",\n  messages,\n  onSendMessage,\n  isLoading = false\n}: MainContentProps) {\n  const [inputValue, setInputValue] = useState('');\n\n  const handleSend = () => {\n    if (inputValue.trim() && onSendMessage) {\n      onSendMessage(inputValue.trim());\n      setInputValue('');\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSend();\n    }\n  };\n\n  return (\n    <div className=\"flex-1 flex flex-col h-full bg-background\">\n      {/* Header */}\n      <div className=\"sticky top-0 z-10 bg-background border-b border-border-main\">\n        <div className=\"px-6 py-4\">\n          <div className=\"text-text-primary text-lg font-medium\">\n            <span className=\"whitespace-nowrap text-ellipsis overflow-hidden\">\n              {title}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Messages Area */}\n      <ScrollArea className=\"flex-1 px-6\">\n        <div className=\"space-y-4 py-4\">\n          {messages.length === 0 ? (\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"text-center\">\n                <div className=\"text-text-tertiary text-lg mb-2\">\n                  No messages yet\n                </div>\n                <div className=\"text-text-tertiary text-sm\">\n                  Start a conversation with Manus Crawler\n                </div>\n              </div>\n            </div>\n          ) : (\n            messages.map((message, index) => (\n              <ChatMessageComponent key={index} message={message} />\n            ))\n          )}\n          {isLoading && (\n            <div className=\"flex items-center space-x-2 text-text-tertiary\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-text-tertiary\"></div>\n              <span>Manus is thinking...</span>\n            </div>\n          )}\n        </div>\n      </ScrollArea>\n\n      {/* Input Area */}\n      <div className=\"border-t border-border-main p-4\">\n        <div className=\"flex items-end space-x-2\">\n          <div className=\"flex-1 relative\">\n            <textarea\n              value={inputValue}\n              onChange={(e) => setInputValue(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Send message to Manus\"\n              className=\"w-full resize-none rounded-lg border border-border-main bg-background-menu-white px-4 py-3 text-text-primary placeholder-text-tertiary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent min-h-[44px] max-h-32\"\n              rows={1}\n              disabled={isLoading}\n            />\n          </div>\n          <button\n            onClick={handleSend}\n            disabled={!inputValue.trim() || isLoading}\n            className=\"flex items-center justify-center w-10 h-10 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed manus-transition\"\n          >\n            <Send className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface ChatMessageComponentProps {\n  message: ChatMessage;\n}\n\nfunction ChatMessageComponent({ message }: ChatMessageComponentProps) {\n  const isUser = message.sender === 'user';\n\n  return (\n    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>\n      <div className={`max-w-[80%] ${isUser ? 'items-end' : 'items-start'} flex flex-col`}>\n        {/* Message Content */}\n        <div\n          className={`rounded-lg px-4 py-3 ${\n            isUser\n              ? 'bg-primary text-primary-foreground rounded-br-none'\n              : 'bg-fill-tsp-white-main text-text-primary rounded-bl-none'\n          }`}\n        >\n          <div className=\"whitespace-pre-wrap break-words\">\n            {message.content}\n          </div>\n\n          {/* Attachments */}\n          {message.attachments && message.attachments.length > 0 && (\n            <div className=\"mt-2 space-y-2\">\n              {message.attachments.map((attachment, index) => (\n                <div\n                  key={index}\n                  className=\"rounded-[10px] bg-fill-tsp-white-main border border-border-light p-3 group/attach\"\n                >\n                  <div className=\"flex items-center space-x-2\">\n                    <Paperclip className=\"h-4 w-4 text-icon-secondary\" />\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"text-sm text-text-primary font-medium truncate\">\n                        {attachment.filename}\n                      </div>\n                      {attachment.details && (\n                        <div className=\"text-xs text-text-tertiary\">\n                          {attachment.details}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Timestamp */}\n        {message.timestamp && (\n          <div className={`text-xs text-text-tertiary mt-1 ${isUser ? 'text-right' : 'text-left'}`}>\n            {message.timestamp}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AALA;;;;;AAcO,SAAS,sBAAsB,EACpC,QAAQ,eAAe,EACvB,QAAQ,EACR,aAAa,EACb,YAAY,KAAK,EACA;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB,IAAI,WAAW,IAAI,MAAM,eAAe;YACtC,cAAc,WAAW,IAAI;YAC7B,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;0BAOT,8OAAC,0IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAkC;;;;;;kDAGjD,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;mCAMhD,SAAS,GAAG,CAAC,CAAC,SAAS,sBACrB,8OAAC;gCAAiC,SAAS;+BAAhB;;;;;wBAG9B,2BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,YAAY;gCACZ,aAAY;gCACZ,WAAU;gCACV,MAAM;gCACN,UAAU;;;;;;;;;;;sCAGd,8OAAC;4BACC,SAAS;4BACT,UAAU,CAAC,WAAW,IAAI,MAAM;4BAChC,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;AAMA,SAAS,qBAAqB,EAAE,OAAO,EAA6B;IAClE,MAAM,SAAS,QAAQ,MAAM,KAAK;IAElC,qBACE,8OAAC;QAAI,WAAW,CAAC,KAAK,EAAE,SAAS,gBAAgB,iBAAiB;kBAChE,cAAA,8OAAC;YAAI,WAAW,CAAC,YAAY,EAAE,SAAS,cAAc,cAAc,cAAc,CAAC;;8BAEjF,8OAAC;oBACC,WAAW,CAAC,qBAAqB,EAC/B,SACI,uDACA,4DACJ;;sCAEF,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,OAAO;;;;;;wBAIjB,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,mBACnD,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBACpC,8OAAC;oCAEC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,WAAW,QAAQ;;;;;;oDAErB,WAAW,OAAO,kBACjB,8OAAC;wDAAI,WAAU;kEACZ,WAAW,OAAO;;;;;;;;;;;;;;;;;;mCAXtB;;;;;;;;;;;;;;;;gBAuBd,QAAQ,SAAS,kBAChB,8OAAC;oBAAI,WAAW,CAAC,gCAAgC,EAAE,SAAS,eAAe,aAAa;8BACrF,QAAQ,SAAS;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 763, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1065, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1214, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { CrawlRequest, CrawledData, SetupProfileRequest, ChatWithManusRequest } from './types';\n\nconst API_BASE_URL = process.env.BACKEND_URL || 'http://localhost:8000';\n\nexport const api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n});\n\n// API Functions\nexport const crawlApi = {\n  // Sync crawl\n  crawlUrl: async (request: Omit<CrawlRequest, 'request_id'>): Promise<CrawledData> => {\n    const response = await api.post('/crawl-url/', request);\n    return response.data;\n  },\n\n  // Realtime crawl (returns immediately, data comes via WebSocket)\n  crawlUrlRealtime: async (request: CrawlRequest): Promise<{ status: string }> => {\n    const response = await api.post('/crawl-url-realtime/', request);\n    return response.data;\n  },\n\n  // Interactive chat with Man<PERSON> (NEW)\n  chatWithManusRealtime: async (request: ChatWithManusRequest): Promise<{ status: string; request_id: string }> => {\n    const response = await api.post('/chat-with-manus-realtime/', request);\n    return response.data;\n  },\n\n  // Health check\n  healthCheck: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n};\n\nexport const adminApi = {\n  // Setup Chrome profile\n  setupProfile: async (\n    request: SetupProfileRequest,\n    apiKey: string\n  ): Promise<{ status: string; message: string }> => {\n    const response = await api.post('/admin/setup-chrome-profile/', request, {\n      headers: { 'X-API-KEY': apiKey },\n    });\n    return response.data;\n  },\n\n  // List profiles\n  listProfiles: async (apiKey: string): Promise<string[]> => {\n    const response = await api.get('/admin/list-profiles/', {\n      headers: { 'X-API-KEY': apiKey },\n    });\n    return response.data;\n  },\n\n  // Delete profile\n  deleteProfile: async (profileName: string, apiKey: string): Promise<{ status: string }> => {\n    const response = await api.delete(`/admin/delete-profile/${profileName}`, {\n      headers: { 'X-API-KEY': apiKey },\n    });\n    return response.data;\n  },\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AAGA,MAAM,eAAe,6DAA2B;AAEzC,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,SAAS;IACT,SAAS;AACX;AAGO,MAAM,WAAW;IACtB,aAAa;IACb,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,eAAe;QAC/C,OAAO,SAAS,IAAI;IACtB;IAEA,iEAAiE;IACjE,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,wBAAwB;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,oCAAoC;IACpC,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,8BAA8B;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAEO,MAAM,WAAW;IACtB,uBAAuB;IACvB,cAAc,OACZ,SACA;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,gCAAgC,SAAS;YACvE,SAAS;gBAAE,aAAa;YAAO;QACjC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,yBAAyB;YACtD,SAAS;gBAAE,aAAa;YAAO;QACjC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,eAAe,OAAO,aAAqB;QACzC,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,sBAAsB,EAAE,aAAa,EAAE;YACxE,SAAS;gBAAE,aAAa;YAAO;QACjC;QACA,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/stores/crawler-store.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { crawlApi } from '@/lib/api';\nimport { CrawlRequest, CrawledData, ChatWithManusRequest } from '@/lib/types';\n\ninterface CrawlerState {\n  results: CrawledData | null;\n  isLoading: boolean;\n  error: string | null;\n  status: string | null;\n  progress: number | undefined;\n  currentRequestId: string | null;\n\n  // Actions\n  startCrawl: (request: CrawlRequest) => Promise<void>;\n  startChat: (request: ChatWithManusRequest) => Promise<void>;\n  setResults: (results: CrawledData) => void;\n  setStatus: (status: string) => void;\n  setProgress: (progress: number) => void;\n  setError: (error: string) => void;\n  clearError: () => void;\n  reset: () => void;\n}\n\nexport const useCrawlerStore = create<CrawlerState>((set, get) => ({\n  results: null,\n  isLoading: false,\n  error: null,\n  status: null,\n  progress: undefined,\n  currentRequestId: null,\n\n  startCrawl: async (request: CrawlRequest) => {\n    set({ \n      isLoading: true, \n      error: null, \n      status: 'Starting crawl...', \n      progress: 0,\n      currentRequestId: request.request_id \n    });\n    \n    try {\n      await crawlApi.crawlUrlRealtime(request);\n      set({ status: 'Crawl started successfully' });\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to start crawl',\n        isLoading: false,\n        status: null,\n        progress: undefined,\n      });\n    }\n  },\n\n  startChat: async (request: ChatWithManusRequest) => {\n    set({ \n      isLoading: true, \n      error: null, \n      status: 'Starting chat...', \n      progress: 0,\n      currentRequestId: request.request_id \n    });\n    \n    try {\n      await crawlApi.chatWithManusRealtime(request);\n      set({ status: 'Chat started successfully' });\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to start chat',\n        isLoading: false,\n        status: null,\n        progress: undefined,\n      });\n    }\n  },\n\n  setResults: (results: CrawledData) => {\n    set({ results, isLoading: false, progress: 100 });\n  },\n\n  setStatus: (status: string) => {\n    set({ status });\n  },\n\n  setProgress: (progress: number) => {\n    set({ progress });\n  },\n\n  setError: (error: string) => {\n    set({ error, isLoading: false, status: null, progress: undefined });\n  },\n\n  clearError: () => {\n    set({ error: null });\n  },\n\n  reset: () => {\n    set({\n      results: null,\n      isLoading: false,\n      error: null,\n      status: null,\n      progress: undefined,\n      currentRequestId: null,\n    });\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAsBO,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAgB,CAAC,KAAK,MAAQ,CAAC;QACjE,SAAS;QACT,WAAW;QACX,OAAO;QACP,QAAQ;QACR,UAAU;QACV,kBAAkB;QAElB,YAAY,OAAO;YACjB,IAAI;gBACF,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,kBAAkB,QAAQ,UAAU;YACtC;YAEA,IAAI;gBACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,gBAAgB,CAAC;gBAChC,IAAI;oBAAE,QAAQ;gBAA6B;YAC7C,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;oBACX,QAAQ;oBACR,UAAU;gBACZ;YACF;QACF;QAEA,WAAW,OAAO;YAChB,IAAI;gBACF,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,kBAAkB,QAAQ,UAAU;YACtC;YAEA,IAAI;gBACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,qBAAqB,CAAC;gBACrC,IAAI;oBAAE,QAAQ;gBAA4B;YAC5C,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;oBACX,QAAQ;oBACR,UAAU;gBACZ;YACF;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;gBAAS,WAAW;gBAAO,UAAU;YAAI;QACjD;QAEA,WAAW,CAAC;YACV,IAAI;gBAAE;YAAO;QACf;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;QACjB;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;gBAAO,WAAW;gBAAO,QAAQ;gBAAM,UAAU;YAAU;QACnE;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,OAAO;YACL,IAAI;gBACF,SAAS;gBACT,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,kBAAkB;YACpB;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/stores/admin-store.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { adminApi } from '@/lib/api';\nimport { SetupProfileRequest } from '@/lib/types';\n\ninterface AdminState {\n  apiKey: string;\n  profiles: string[];\n  isLoading: boolean;\n  error: string | null;\n\n  // Actions\n  setApiKey: (key: string) => void;\n  loadProfiles: () => Promise<void>;\n  setupProfile: (request: SetupProfileRequest) => Promise<boolean>;\n  deleteProfile: (profileName: string) => Promise<boolean>;\n  clearError: () => void;\n}\n\nexport const useAdminStore = create<AdminState>((set, get) => ({\n  apiKey: '',\n  profiles: [],\n  isLoading: false,\n  error: null,\n\n  setApiKey: (key: string) => {\n    set({ apiKey: key });\n  },\n\n  loadProfiles: async () => {\n    const { apiKey } = get();\n    if (!apiKey) {\n      set({ error: 'API Key is required' });\n      return;\n    }\n\n    set({ isLoading: true, error: null });\n    try {\n      const profiles = await adminApi.listProfiles(apiKey);\n      // Ensure profiles is always an array\n      set({ profiles: Array.isArray(profiles) ? profiles : [], isLoading: false });\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to load profiles',\n        isLoading: false,\n        profiles: [] // Reset to empty array on error\n      });\n    }\n  },\n\n  setupProfile: async (request: SetupProfileRequest) => {\n    const { apiKey } = get();\n    if (!apiKey) {\n      set({ error: 'API Key is required' });\n      return false;\n    }\n\n    set({ isLoading: true, error: null });\n    try {\n      await adminApi.setupProfile(request, apiKey);\n      await get().loadProfiles(); // Reload profiles\n      set({ isLoading: false });\n      return true;\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to setup profile',\n        isLoading: false\n      });\n      return false;\n    }\n  },\n\n  deleteProfile: async (profileName: string) => {\n    const { apiKey } = get();\n    if (!apiKey) {\n      set({ error: 'API Key is required' });\n      return false;\n    }\n\n    set({ isLoading: true, error: null });\n    try {\n      await adminApi.deleteProfile(profileName, apiKey);\n      await get().loadProfiles(); // Reload profiles\n      set({ isLoading: false });\n      return true;\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to delete profile',\n        isLoading: false\n      });\n      return false;\n    }\n  },\n\n  clearError: () => {\n    set({ error: null });\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAiBO,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAc,CAAC,KAAK,MAAQ,CAAC;QAC7D,QAAQ;QACR,UAAU,EAAE;QACZ,WAAW;QACX,OAAO;QAEP,WAAW,CAAC;YACV,IAAI;gBAAE,QAAQ;YAAI;QACpB;QAEA,cAAc;YACZ,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,CAAC,QAAQ;gBACX,IAAI;oBAAE,OAAO;gBAAsB;gBACnC;YACF;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC;gBAC7C,qCAAqC;gBACrC,IAAI;oBAAE,UAAU,MAAM,OAAO,CAAC,YAAY,WAAW,EAAE;oBAAE,WAAW;gBAAM;YAC5E,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;oBACX,UAAU,EAAE,CAAC,gCAAgC;gBAC/C;YACF;QACF;QAEA,cAAc,OAAO;YACnB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,CAAC,QAAQ;gBACX,IAAI;oBAAE,OAAO;gBAAsB;gBACnC,OAAO;YACT;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS;gBACrC,MAAM,MAAM,YAAY,IAAI,kBAAkB;gBAC9C,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,OAAO;YACT;QACF;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,CAAC,QAAQ;gBACX,IAAI;oBAAE,OAAO;gBAAsB;gBACnC,OAAO;YACT;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,aAAa,CAAC,aAAa;gBAC1C,MAAM,MAAM,YAAY,IAAI,kBAAkB;gBAC9C,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,OAAO;YACT;QACF;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/crawler/crawler-form.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { useCrawlerStore } from '@/stores/crawler-store';\nimport { useAdminStore } from '@/stores/admin-store';\nimport { Bug, Settings } from 'lucide-react';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface CrawlerFormProps {\n  onStartCrawl?: () => void;\n}\n\nexport function CrawlerForm({ onStartCrawl }: CrawlerFormProps) {\n  const [url, setUrl] = useState('https://manus.im/');\n  const [selectedProfile, setSelectedProfile] = useState<string>('');\n  const [headless, setHeadless] = useState(true);\n\n  const { startCrawl, isLoading, status } = useCrawlerStore();\n  const { profiles } = useAdminStore();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!url.trim()) return;\n\n    const requestId = uuidv4();\n\n    await startCrawl({\n      url: url.trim(),\n      profile_name: selectedProfile === '__default__' ? undefined : selectedProfile || undefined,\n      headless,\n      request_id: requestId\n    });\n\n    onStartCrawl?.();\n  };\n\n  return (\n    <div className=\"w-full max-w-3xl space-y-6\">\n      {/* Status Display */}\n      {(isLoading || status) && (\n        <Card className=\"border-primary/20 bg-gradient-to-r from-primary/5 to-transparent\">\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center space-x-3\">\n              {isLoading && (\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-primary\"></div>\n              )}\n              <div>\n                <p className=\"text-sm font-medium text-primary\">\n                  {isLoading ? 'Crawling in progress...' : 'Ready to crawl'}\n                </p>\n                {status && (\n                  <p className=\"text-xs text-text-tertiary mt-1\">{status}</p>\n                )}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      <Card className=\"border-border-main bg-gradient-to-br from-fill-tsp-white-main to-fill-tsp-white-dark shadow-xl\">\n        <CardHeader className=\"pb-4\">\n          <CardTitle className=\"flex items-center space-x-3 text-xl\">\n            <div className=\"p-2 rounded-lg bg-primary/10\">\n              <Bug className=\"h-6 w-6 text-primary\" />\n            </div>\n            <span>Start Crawling</span>\n          </CardTitle>\n          <CardDescription className=\"text-text-tertiary\">\n            Configure and start crawling a Manus.im page with realtime updates\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* URL Input */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium text-text-primary flex items-center space-x-2\">\n              <span>Target URL</span>\n              <span className=\"text-xs text-text-tertiary\">(Required)</span>\n            </label>\n            <Input\n              type=\"url\"\n              placeholder=\"https://manus.im/\"\n              value={url}\n              onChange={(e) => setUrl(e.target.value)}\n              required\n              className=\"h-12 text-base border-border-main focus:border-primary focus:ring-primary/20\"\n            />\n            <p className=\"text-xs text-text-tertiary\">\n              Enter the Manus.im page URL you want to crawl\n            </p>\n          </div>\n\n          {/* Profile Selection */}\n          <div>\n            <label className=\"text-sm font-medium text-text-primary mb-2 block\">\n              Chrome Profile (Optional)\n            </label>\n            <Select value={selectedProfile} onValueChange={setSelectedProfile}>\n              <SelectTrigger>\n                <SelectValue placeholder=\"Select a profile or leave empty for default\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"__default__\">Default (No Profile)</SelectItem>\n                {profiles.map((profile) => (\n                  <SelectItem key={profile} value={profile}>\n                    {profile}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Headless Mode */}\n          <div className=\"flex items-center space-x-2\">\n            <Checkbox\n              id=\"headless\"\n              checked={headless}\n              onCheckedChange={(checked) => setHeadless(checked as boolean)}\n            />\n            <label\n              htmlFor=\"headless\"\n              className=\"text-sm font-medium text-text-primary cursor-pointer\"\n            >\n              Run in headless mode (recommended)\n            </label>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"pt-4 border-t border-border-light\">\n            <Button\n              type=\"submit\"\n              disabled={isLoading || !url.trim()}\n              className=\"w-full h-12 text-base font-medium bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:shadow-xl transition-all duration-300\"\n            >\n              {isLoading ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3\"></div>\n                  <span>Crawling in Progress...</span>\n                </>\n              ) : (\n                <>\n                  <Bug className=\"h-5 w-5 mr-3\" />\n                  <span>Start Crawling</span>\n                </>\n              )}\n            </Button>\n            <p className=\"text-xs text-text-tertiary text-center mt-3\">\n              Click to start crawling with realtime updates via WebSocket\n            </p>\n          </div>\n        </form>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAiBO,SAAS,YAAY,EAAE,YAAY,EAAoB;IAC5D,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD;IACxD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;IAEjC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,IAAI,IAAI,IAAI;QAEjB,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,KAAM,AAAD;QAEvB,MAAM,WAAW;YACf,KAAK,IAAI,IAAI;YACb,cAAc,oBAAoB,gBAAgB,YAAY,mBAAmB;YACjF;YACA,YAAY;QACd;QAEA;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,CAAC,aAAa,MAAM,mBACnB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,2BACC,8OAAC;gCAAI,WAAU;;;;;;0CAEjB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDACV,YAAY,4BAA4B;;;;;;oCAE1C,wBACC,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5D,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,kBAAe;gCAAC,WAAU;0CAAqB;;;;;;;;;;;;kCAIlD,8OAAC,gIAAA,CAAA,cAAW;kCACZ,cAAA,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAE/C,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;4CACtC,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAM5C,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAmD;;;;;;sDAGpE,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAiB,eAAe;;8DAC7C,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAc;;;;;;wDAC/B,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,kIAAA,CAAA,aAAU;gEAAe,OAAO;0EAC9B;+DADc;;;;;;;;;;;;;;;;;;;;;;;8CASzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS;4CACT,iBAAiB,CAAC,UAAY,YAAY;;;;;;sDAE5C,8OAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU,aAAa,CAAC,IAAI,IAAI;4CAChC,WAAU;sDAET,0BACC;;kEACE,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;;6EAGR;;kEACE,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;;;;;;;;sDAIZ,8OAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvE", "debugId": null}}, {"offset": {"line": 1888, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/lib/websocket.ts"], "sourcesContent": ["import { WebSocketMessage } from './types';\n\nexport class WebSocketManager {\n  private ws: WebSocket | null = null;\n  private url: string;\n  private requestId: string;\n  private onMessage: (message: WebSocketMessage) => void;\n  private onError: (error: Event) => void;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n\n  constructor(\n    requestId: string,\n    onMessage: (message: WebSocketMessage) => void,\n    onError: (error: Event) => void = () => {}\n  ) {\n    this.requestId = requestId;\n    this.onMessage = onMessage;\n    this.onError = onError;\n    this.url = `${process.env.WS_URL || 'ws://localhost:8000'}/ws/crawl-status/${requestId}`;\n  }\n\n  connect(): void {\n    try {\n      this.ws = new WebSocket(this.url);\n\n      this.ws.onopen = () => {\n        console.log(`WebSocket connected for request: ${this.requestId}`);\n        this.reconnectAttempts = 0;\n      };\n\n      this.ws.onmessage = (event) => {\n        try {\n          const message: WebSocketMessage = JSON.parse(event.data);\n          this.onMessage(message);\n        } catch (error) {\n          console.error('Failed to parse WebSocket message:', error);\n        }\n      };\n\n      this.ws.onclose = () => {\n        console.log('WebSocket connection closed');\n        this.attemptReconnect();\n      };\n\n      this.ws.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        this.onError(error);\n      };\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      this.onError(error as Event);\n    }\n  }\n\n  private attemptReconnect(): void {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n\n      setTimeout(() => {\n        this.connect();\n      }, 1000 * this.reconnectAttempts);\n    }\n  }\n\n  disconnect(): void {\n    if (this.ws) {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n\n  isConnected(): boolean {\n    return this.ws?.readyState === WebSocket.OPEN;\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACH,KAAuB,KAAK;IAC5B,IAAY;IACZ,UAAkB;IAClB,UAA+C;IAC/C,QAAgC;IAChC,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;IAEjC,YACE,SAAiB,EACjB,SAA8C,EAC9C,UAAkC,KAAO,CAAC,CAC1C;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,GAAG,GAAG,GAAG,2DAAsB,sBAAsB,iBAAiB,EAAE,WAAW;IAC1F;IAEA,UAAgB;QACd,IAAI;YACF,IAAI,CAAC,EAAE,GAAG,IAAI,UAAU,IAAI,CAAC,GAAG;YAEhC,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG;gBACf,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,SAAS,EAAE;gBAChE,IAAI,CAAC,iBAAiB,GAAG;YAC3B;YAEA,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;gBACnB,IAAI;oBACF,MAAM,UAA4B,KAAK,KAAK,CAAC,MAAM,IAAI;oBACvD,IAAI,CAAC,SAAS,CAAC;gBACjB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACtD;YACF;YAEA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG;gBAChB,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,gBAAgB;YACvB;YAEA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC;gBACjB,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,IAAI,CAAC,OAAO,CAAC;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,IAAI,CAAC,OAAO,CAAC;QACf;IACF;IAEQ,mBAAyB;QAC/B,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACtD,IAAI,CAAC,iBAAiB;YACtB,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;YAEjG,WAAW;gBACT,IAAI,CAAC,OAAO;YACd,GAAG,OAAO,IAAI,CAAC,iBAAiB;QAClC;IACF;IAEA,aAAmB;QACjB,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,IAAI,CAAC,EAAE,CAAC,KAAK;YACb,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;IAEA,cAAuB;QACrB,OAAO,IAAI,CAAC,EAAE,EAAE,eAAe,UAAU,IAAI;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 1958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/hooks/use-websocket.ts"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\nimport { WebSocketManager } from '@/lib/websocket';\nimport { WebSocketMessage } from '@/lib/types';\n\nexport function useWebSocket(requestId: string | null) {\n  const [isConnected, setIsConnected] = useState(false);\n  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  const wsManager = useRef<WebSocketManager | null>(null);\n\n  useEffect(() => {\n    if (!requestId) return;\n\n    const handleMessage = (message: WebSocketMessage) => {\n      setLastMessage(message);\n    };\n\n    const handleError = (error: Event) => {\n      setError('WebSocket connection error');\n      setIsConnected(false);\n    };\n\n    wsManager.current = new WebSocketManager(requestId, handleMessage, handleError);\n    wsManager.current.connect();\n\n    const checkConnection = setInterval(() => {\n      if (wsManager.current) {\n        setIsConnected(wsManager.current.isConnected());\n      }\n    }, 1000);\n\n    return () => {\n      clearInterval(checkConnection);\n      if (wsManager.current) {\n        wsManager.current.disconnect();\n        wsManager.current = null;\n      }\n    };\n  }, [requestId]);\n\n  return {\n    isConnected,\n    lastMessage,\n    error,\n    clearError: () => setError(null),\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,SAAS,aAAa,SAAwB;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,MAAM,gBAAgB,CAAC;YACrB,eAAe;QACjB;QAEA,MAAM,cAAc,CAAC;YACnB,SAAS;YACT,eAAe;QACjB;QAEA,UAAU,OAAO,GAAG,IAAI,uHAAA,CAAA,mBAAgB,CAAC,WAAW,eAAe;QACnE,UAAU,OAAO,CAAC,OAAO;QAEzB,MAAM,kBAAkB,YAAY;YAClC,IAAI,UAAU,OAAO,EAAE;gBACrB,eAAe,UAAU,OAAO,CAAC,WAAW;YAC9C;QACF,GAAG;QAEH,OAAO;YACL,cAAc;YACd,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,UAAU;gBAC5B,UAAU,OAAO,GAAG;YACtB;QACF;IACF,GAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA,YAAY,IAAM,SAAS;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/app/crawler/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ManusStyleSidebar } from '@/components/manus/sidebar';\nimport { ManusStyleMainContent } from '@/components/manus/main-content';\nimport { CrawlerForm } from '@/components/crawler/crawler-form';\nimport { useCrawlerStore } from '@/stores/crawler-store';\nimport { useAdminStore } from '@/stores/admin-store';\nimport { useWebSocket } from '@/hooks/use-websocket';\nimport { TaskItem, ChatMessage, CrawledData, InteractiveChatResult } from '@/lib/types';\nimport { v4 as uuidv4 } from 'uuid';\n\nexport default function CrawlerPage() {\n  const {\n    results,\n    isLoading,\n    currentRequestId,\n    setResults,\n    setStatus,\n    setError,\n    startChat\n  } = useCrawlerStore();\n\n  const { loadProfiles } = useAdminStore();\n  const [selectedTask, setSelectedTask] = useState<TaskItem | null>(null);\n  const [showForm, setShowForm] = useState(true);\n\n  // WebSocket connection\n  const { lastMessage, isConnected } = useWebSocket(currentRequestId);\n\n  // Load profiles on mount\n  useEffect(() => {\n    loadProfiles();\n  }, [loadProfiles]);\n\n  // Handle WebSocket messages\n  useEffect(() => {\n    if (lastMessage) {\n      switch (lastMessage.type) {\n        case 'progress':\n          setStatus(lastMessage.message);\n          break;\n        case 'data':\n          if (lastMessage.data) {\n            // Check if it's crawled data or chat result\n            if ('chat_response' in lastMessage.data) {\n              // It's an interactive chat result\n              const chatResult = lastMessage.data as InteractiveChatResult;\n              if (chatResult.updated_page_data) {\n                setResults(chatResult.updated_page_data);\n              }\n            } else {\n              // It's regular crawled data\n              setResults(lastMessage.data as CrawledData);\n              setShowForm(false); // Hide form after successful crawl\n            }\n          }\n          break;\n        case 'error':\n          setError(lastMessage.message);\n          break;\n      }\n    }\n  }, [lastMessage, setResults, setStatus, setError]);\n\n  // Convert crawled data to display format\n  const tasks: TaskItem[] = results?.tasks || [];\n  const messages: ChatMessage[] = results?.chat_messages || [];\n\n  const handleTaskSelect = (task: TaskItem) => {\n    setSelectedTask(task);\n  };\n\n  const handleNewTask = () => {\n    setShowForm(true);\n    setSelectedTask(null);\n  };\n\n  const handleStartCrawl = () => {\n    setShowForm(false);\n  };\n\n  const handleSendMessage = async (message: string) => {\n    if (!results?.page_title) return;\n\n    const requestId = uuidv4();\n\n    await startChat({\n      message,\n      task_url: window.location.href, // Current page URL\n      profile_name: undefined, // Could be made configurable\n      request_id: requestId,\n      headless: true\n    });\n  };\n\n  return (\n    <div className=\"flex w-full h-screen overflow-hidden\">\n      {/* Sidebar */}\n      <ManusStyleSidebar\n        tasks={tasks}\n        onTaskSelect={handleTaskSelect}\n        onNewTask={handleNewTask}\n      />\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col\">\n        {showForm ? (\n          <div className=\"flex-1 flex items-center justify-center p-6\">\n            <CrawlerForm onStartCrawl={handleStartCrawl} />\n          </div>\n        ) : (\n          <ManusStyleMainContent\n            title={selectedTask?.title || results?.page_title || \"Manus Crawler\"}\n            messages={messages}\n            onSendMessage={handleSendMessage}\n            isLoading={isLoading}\n          />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAVA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,EACJ,OAAO,EACP,SAAS,EACT,gBAAgB,EAChB,UAAU,EACV,SAAS,EACT,QAAQ,EACR,SAAS,EACV,GAAG,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,uBAAuB;IACvB,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;IAElD,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAa;IAEjB,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,OAAQ,YAAY,IAAI;gBACtB,KAAK;oBACH,UAAU,YAAY,OAAO;oBAC7B;gBACF,KAAK;oBACH,IAAI,YAAY,IAAI,EAAE;wBACpB,4CAA4C;wBAC5C,IAAI,mBAAmB,YAAY,IAAI,EAAE;4BACvC,kCAAkC;4BAClC,MAAM,aAAa,YAAY,IAAI;4BACnC,IAAI,WAAW,iBAAiB,EAAE;gCAChC,WAAW,WAAW,iBAAiB;4BACzC;wBACF,OAAO;4BACL,4BAA4B;4BAC5B,WAAW,YAAY,IAAI;4BAC3B,YAAY,QAAQ,mCAAmC;wBACzD;oBACF;oBACA;gBACF,KAAK;oBACH,SAAS,YAAY,OAAO;oBAC5B;YACJ;QACF;IACF,GAAG;QAAC;QAAa;QAAY;QAAW;KAAS;IAEjD,yCAAyC;IACzC,MAAM,QAAoB,SAAS,SAAS,EAAE;IAC9C,MAAM,WAA0B,SAAS,iBAAiB,EAAE;IAE5D,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,gBAAgB;QACpB,YAAY;QACZ,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,YAAY;IACd;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,SAAS,YAAY;QAE1B,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,KAAM,AAAD;QAEvB,MAAM,UAAU;YACd;YACA,UAAU,OAAO,QAAQ,CAAC,IAAI;YAC9B,cAAc;YACd,YAAY;YACZ,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,sIAAA,CAAA,oBAAiB;gBAChB,OAAO;gBACP,cAAc;gBACd,WAAW;;;;;;0BAIb,8OAAC;gBAAI,WAAU;0BACZ,yBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gJAAA,CAAA,cAAW;wBAAC,cAAc;;;;;;;;;;yCAG7B,8OAAC,8IAAA,CAAA,wBAAqB;oBACpB,OAAO,cAAc,SAAS,SAAS,cAAc;oBACrD,UAAU;oBACV,eAAe;oBACf,WAAW;;;;;;;;;;;;;;;;;AAMvB", "debugId": null}}]}