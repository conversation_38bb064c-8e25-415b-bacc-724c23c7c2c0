{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { CrawlRequest, CrawledData, SetupProfileRequest, ChatWithManusRequest } from './types';\n\nconst API_BASE_URL = process.env.BACKEND_URL || 'http://localhost:8000';\n\nexport const api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n});\n\n// API Functions\nexport const crawlApi = {\n  // Sync crawl\n  crawlUrl: async (request: Omit<CrawlRequest, 'request_id'>): Promise<CrawledData> => {\n    const response = await api.post('/crawl-url/', request);\n    return response.data;\n  },\n\n  // Realtime crawl (returns immediately, data comes via WebSocket)\n  crawlUrlRealtime: async (request: CrawlRequest): Promise<{ status: string }> => {\n    const response = await api.post('/crawl-url-realtime/', request);\n    return response.data;\n  },\n\n  // Interactive chat with Man<PERSON> (NEW)\n  chatWithManusRealtime: async (request: ChatWithManusRequest): Promise<{ status: string; request_id: string }> => {\n    const response = await api.post('/chat-with-manus-realtime/', request);\n    return response.data;\n  },\n\n  // Health check\n  healthCheck: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n};\n\nexport const adminApi = {\n  // Setup Chrome profile\n  setupProfile: async (\n    request: SetupProfileRequest,\n    apiKey: string\n  ): Promise<{ status: string; message: string }> => {\n    const response = await api.post('/admin/setup-chrome-profile/', request, {\n      headers: { 'X-API-KEY': apiKey },\n    });\n    return response.data;\n  },\n\n  // List profiles\n  listProfiles: async (apiKey: string): Promise<string[]> => {\n    const response = await api.get('/admin/list-profiles/', {\n      headers: { 'X-API-KEY': apiKey },\n    });\n    return response.data;\n  },\n\n  // Delete profile\n  deleteProfile: async (profileName: string, apiKey: string): Promise<{ status: string }> => {\n    const response = await api.delete(`/admin/delete-profile/${profileName}`, {\n      headers: { 'X-API-KEY': apiKey },\n    });\n    return response.data;\n  },\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AAGA,MAAM,eAAe,6DAA2B;AAEzC,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,SAAS;IACT,SAAS;AACX;AAGO,MAAM,WAAW;IACtB,aAAa;IACb,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,eAAe;QAC/C,OAAO,SAAS,IAAI;IACtB;IAEA,iEAAiE;IACjE,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,wBAAwB;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,oCAAoC;IACpC,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,8BAA8B;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAEO,MAAM,WAAW;IACtB,uBAAuB;IACvB,cAAc,OACZ,SACA;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,gCAAgC,SAAS;YACvE,SAAS;gBAAE,aAAa;YAAO;QACjC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,yBAAyB;YACtD,SAAS;gBAAE,aAAa;YAAO;QACjC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,eAAe,OAAO,aAAqB;QACzC,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,sBAAsB,EAAE,aAAa,EAAE;YACxE,SAAS;gBAAE,aAAa;YAAO;QACjC;QACA,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/stores/admin-store.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { adminApi } from '@/lib/api';\nimport { SetupProfileRequest } from '@/lib/types';\n\ninterface AdminState {\n  apiKey: string;\n  profiles: string[];\n  isLoading: boolean;\n  error: string | null;\n\n  // Actions\n  setApiKey: (key: string) => void;\n  loadProfiles: () => Promise<void>;\n  setupProfile: (request: SetupProfileRequest) => Promise<boolean>;\n  deleteProfile: (profileName: string) => Promise<boolean>;\n  clearError: () => void;\n}\n\nexport const useAdminStore = create<AdminState>((set, get) => ({\n  apiKey: '',\n  profiles: [],\n  isLoading: false,\n  error: null,\n\n  setApiKey: (key: string) => {\n    set({ apiKey: key });\n  },\n\n  loadProfiles: async () => {\n    const { apiKey } = get();\n    if (!apiKey) {\n      set({ error: 'API Key is required' });\n      return;\n    }\n\n    set({ isLoading: true, error: null });\n    try {\n      const profiles = await adminApi.listProfiles(apiKey);\n      // Ensure profiles is always an array\n      set({ profiles: Array.isArray(profiles) ? profiles : [], isLoading: false });\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to load profiles',\n        isLoading: false,\n        profiles: [] // Reset to empty array on error\n      });\n    }\n  },\n\n  setupProfile: async (request: SetupProfileRequest) => {\n    const { apiKey } = get();\n    if (!apiKey) {\n      set({ error: 'API Key is required' });\n      return false;\n    }\n\n    set({ isLoading: true, error: null });\n    try {\n      await adminApi.setupProfile(request, apiKey);\n      await get().loadProfiles(); // Reload profiles\n      set({ isLoading: false });\n      return true;\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to setup profile',\n        isLoading: false\n      });\n      return false;\n    }\n  },\n\n  deleteProfile: async (profileName: string) => {\n    const { apiKey } = get();\n    if (!apiKey) {\n      set({ error: 'API Key is required' });\n      return false;\n    }\n\n    set({ isLoading: true, error: null });\n    try {\n      await adminApi.deleteProfile(profileName, apiKey);\n      await get().loadProfiles(); // Reload profiles\n      set({ isLoading: false });\n      return true;\n    } catch (error) {\n      set({\n        error: error instanceof Error ? error.message : 'Failed to delete profile',\n        isLoading: false\n      });\n      return false;\n    }\n  },\n\n  clearError: () => {\n    set({ error: null });\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAiBO,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAc,CAAC,KAAK,MAAQ,CAAC;QAC7D,QAAQ;QACR,UAAU,EAAE;QACZ,WAAW;QACX,OAAO;QAEP,WAAW,CAAC;YACV,IAAI;gBAAE,QAAQ;YAAI;QACpB;QAEA,cAAc;YACZ,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,CAAC,QAAQ;gBACX,IAAI;oBAAE,OAAO;gBAAsB;gBACnC;YACF;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC;gBAC7C,qCAAqC;gBACrC,IAAI;oBAAE,UAAU,MAAM,OAAO,CAAC,YAAY,WAAW,EAAE;oBAAE,WAAW;gBAAM;YAC5E,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;oBACX,UAAU,EAAE,CAAC,gCAAgC;gBAC/C;YACF;QACF;QAEA,cAAc,OAAO;YACnB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,CAAC,QAAQ;gBACX,IAAI;oBAAE,OAAO;gBAAsB;gBACnC,OAAO;YACT;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS;gBACrC,MAAM,MAAM,YAAY,IAAI,kBAAkB;gBAC9C,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,OAAO;YACT;QACF;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,CAAC,QAAQ;gBACX,IAAI;oBAAE,OAAO;gBAAsB;gBACnC,OAAO;YACT;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,aAAa,CAAC,aAAa;gBAC1C,MAAM,MAAM,YAAY,IAAI,kBAAkB;gBAC9C,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,OAAO;YACT;QACF;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;IACF,CAAC", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAdminStore } from '@/stores/admin-store';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Trash2, Plus, Key, RefreshCw, ArrowLeft } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function AdminPage() {\n  const {\n    apiKey,\n    profiles,\n    isLoading,\n    error,\n    setApiKey,\n    loadProfiles,\n    setupProfile,\n    deleteProfile,\n    clearError\n  } = useAdminStore();\n\n  const [showApiKeyInput, setShowApiKeyInput] = useState(!apiKey);\n  const [tempApiKey, setTempApiKey] = useState('');\n  const [newProfileName, setNewProfileName] = useState('');\n  const [newProfileUrl, setNewProfileUrl] = useState('https://manus.im/');\n\n  useEffect(() => {\n    if (apiKey) {\n      loadProfiles();\n    }\n  }, [apiKey, loadProfiles]);\n\n  const handleSetApiKey = () => {\n    if (tempApiKey.trim()) {\n      setApiKey(tempApiKey.trim());\n      setShowApiKeyInput(false);\n      setTempApiKey('');\n    }\n  };\n\n  const handleCreateProfile = async () => {\n    if (newProfileName.trim()) {\n      const success = await setupProfile({\n        profile_name: newProfileName.trim(),\n        url: newProfileUrl\n      });\n      if (success) {\n        setNewProfileName('');\n        setNewProfileUrl('https://manus.im/');\n      }\n    }\n  };\n\n  const handleDeleteProfile = async (profileName: string) => {\n    if (confirm(`Are you sure you want to delete profile \"${profileName}\"?`)) {\n      await deleteProfile(profileName);\n    }\n  };\n\n  return (\n    <div className=\"flex-1 p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Button variant=\"ghost\" size=\"icon\" asChild>\n            <Link href=\"/\">\n              <ArrowLeft className=\"h-4 w-4\" />\n            </Link>\n          </Button>\n          <div>\n            <h1 className=\"text-3xl font-bold text-text-primary\">Admin Dashboard</h1>\n            <p className=\"text-text-secondary\">Manage Chrome profiles and crawler settings</p>\n          </div>\n        </div>\n        <Button\n          onClick={() => loadProfiles()}\n          disabled={isLoading || !apiKey}\n          variant=\"outline\"\n          size=\"sm\"\n        >\n          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />\n          Refresh\n        </Button>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"bg-destructive/10 border border-destructive/20 rounded-lg p-4\">\n          <div className=\"flex items-center justify-between\">\n            <p className=\"text-destructive\">{error}</p>\n            <Button variant=\"ghost\" size=\"sm\" onClick={clearError}>\n              ×\n            </Button>\n          </div>\n        </div>\n      )}\n\n      {/* API Key Section */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Key className=\"h-5 w-5\" />\n            <span>API Key Configuration</span>\n          </CardTitle>\n          <CardDescription>\n            Enter your admin API key to manage Chrome profiles\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {showApiKeyInput ? (\n            <div className=\"flex space-x-2\">\n              <Input\n                type=\"password\"\n                placeholder=\"Enter admin API key\"\n                value={tempApiKey}\n                onChange={(e) => setTempApiKey(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && handleSetApiKey()}\n              />\n              <Button onClick={handleSetApiKey} disabled={!tempApiKey.trim()}>\n                Set Key\n              </Button>\n            </div>\n          ) : (\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-text-secondary\">API Key is configured</span>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setShowApiKeyInput(true)}\n              >\n                Change Key\n              </Button>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Profile Management */}\n      {apiKey && (\n        <>\n          {/* Create New Profile */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Plus className=\"h-5 w-5\" />\n                <span>Create New Profile</span>\n              </CardTitle>\n              <CardDescription>\n                Set up a new Chrome profile for crawling\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-sm font-medium text-text-primary mb-2 block\">\n                    Profile Name\n                  </label>\n                  <Input\n                    placeholder=\"e.g., my-profile\"\n                    value={newProfileName}\n                    onChange={(e) => setNewProfileName(e.target.value)}\n                  />\n                </div>\n                <div>\n                  <label className=\"text-sm font-medium text-text-primary mb-2 block\">\n                    Initial URL\n                  </label>\n                  <Input\n                    placeholder=\"https://manus.im/\"\n                    value={newProfileUrl}\n                    onChange={(e) => setNewProfileUrl(e.target.value)}\n                  />\n                </div>\n              </div>\n              <Button\n                onClick={handleCreateProfile}\n                disabled={isLoading || !newProfileName.trim()}\n                className=\"w-full md:w-auto\"\n              >\n                {isLoading ? 'Creating...' : 'Create Profile'}\n              </Button>\n            </CardContent>\n          </Card>\n\n          {/* Existing Profiles */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Existing Profiles ({Array.isArray(profiles) ? profiles.length : 0})</CardTitle>\n              <CardDescription>\n                Manage your Chrome profiles for crawling\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {!Array.isArray(profiles) || profiles.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <p className=\"text-text-tertiary\">No profiles found</p>\n                  <p className=\"text-text-tertiary text-sm\">Create a new profile to get started</p>\n                </div>\n              ) : (\n                <div className=\"space-y-2\">\n                  {profiles.map((profile) => (\n                    <div\n                      key={profile}\n                      className=\"flex items-center justify-between p-3 rounded-lg bg-fill-tsp-white-main border border-border-light\"\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center\">\n                          <span className=\"text-sm font-medium text-primary\">\n                            {profile.charAt(0).toUpperCase()}\n                          </span>\n                        </div>\n                        <span className=\"text-text-primary font-medium\">{profile}</span>\n                      </div>\n                      <Button\n                        variant=\"destructive\"\n                        size=\"sm\"\n                        onClick={() => handleDeleteProfile(profile)}\n                        disabled={isLoading}\n                      >\n                        <Trash2 className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,EACJ,MAAM,EACN,QAAQ,EACR,SAAS,EACT,KAAK,EACL,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,UAAU,EACX,GAAG,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;IAEhB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;KAAa;IAEzB,MAAM,kBAAkB;QACtB,IAAI,WAAW,IAAI,IAAI;YACrB,UAAU,WAAW,IAAI;YACzB,mBAAmB;YACnB,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,eAAe,IAAI,IAAI;YACzB,MAAM,UAAU,MAAM,aAAa;gBACjC,cAAc,eAAe,IAAI;gBACjC,KAAK;YACP;YACA,IAAI,SAAS;gBACX,kBAAkB;gBAClB,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,QAAQ,CAAC,yCAAyC,EAAE,YAAY,EAAE,CAAC,GAAG;YACxE,MAAM,cAAc;QACtB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,OAAO;0CACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;kCAGvC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM;wBACf,UAAU,aAAa,CAAC;wBACxB,SAAQ;wBACR,MAAK;;0CAEL,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAW,CAAC,aAAa,EAAE,YAAY,iBAAiB,IAAI;;;;;;4BAAI;;;;;;;;;;;;;YAM9E,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAoB;;;;;;sCACjC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;sCAAY;;;;;;;;;;;;;;;;;0BAQ7D,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACT,gCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;8CAE1C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAiB,UAAU,CAAC,WAAW,IAAI;8CAAI;;;;;;;;;;;iDAKlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,mBAAmB;8CACnC;;;;;;;;;;;;;;;;;;;;;;;YASR,wBACC;;kCAEE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAmD;;;;;;kEAGpE,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGrD,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAmD;;;;;;kEAGpE,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;kDAItD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,aAAa,CAAC,eAAe,IAAI;wCAC3C,WAAU;kDAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;kCAMnC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;;4CAAC;4CAAoB,MAAM,OAAO,CAAC,YAAY,SAAS,MAAM,GAAG;4CAAE;;;;;;;kDAC7E,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACT,CAAC,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,KAAK,kBAC/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;yDAG5C,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,QAAQ,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sEAGlC,8OAAC;4DAAK,WAAU;sEAAiC;;;;;;;;;;;;8DAEnD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,oBAAoB;oDACnC,UAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;2CAjBf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6B3B", "debugId": null}}]}