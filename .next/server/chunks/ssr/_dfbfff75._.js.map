{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Youhome-fe/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { <PERSON>, Settings, Home } from \"lucide-react\";\n\nexport default function HomePage() {\n  return (\n    <div className=\"flex-1 flex items-center justify-center p-6\">\n      <div className=\"max-w-4xl w-full space-y-8\">\n        {/* Header */}\n        <div className=\"text-center space-y-4\">\n          <div className=\"flex items-center justify-center space-x-2\">\n            <Spider className=\"h-8 w-8 text-primary\" />\n            <h1 className=\"text-4xl font-bold text-text-primary\">Manus Crawler</h1>\n          </div>\n          <p className=\"text-xl text-text-secondary max-w-2xl mx-auto\">\n            Modern NextJS frontend for Manus.im crawler with realtime updates and interactive chat\n          </p>\n        </div>\n\n        {/* Feature Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <Card className=\"manus-hover manus-transition\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Spider className=\"h-5 w-5\" />\n                <span>Crawler Interface</span>\n              </CardTitle>\n              <CardDescription>\n                Crawl Manus.im pages with realtime updates and WebSocket integration\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button asChild className=\"w-full\">\n                <Link href=\"/crawler\">\n                  Start Crawling\n                </Link>\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card className=\"manus-hover manus-transition\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Settings className=\"h-5 w-5\" />\n                <span>Admin Dashboard</span>\n              </CardTitle>\n              <CardDescription>\n                Manage Chrome profiles and configure crawler settings\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button asChild variant=\"outline\" className=\"w-full\">\n                <Link href=\"/admin\">\n                  Admin Panel\n                </Link>\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Features List */}\n        <div className=\"bg-fill-tsp-white-main rounded-lg p-6 space-y-4\">\n          <h2 className=\"text-2xl font-semibold text-text-primary\">Features</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-text-secondary\">\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n                <span>NextJS 14+ App Router with TypeScript</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n                <span>Realtime WebSocket updates</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n                <span>Manus-style UI with dark theme</span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n                <span>Interactive chat with Manus</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n                <span>Chrome profile management</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n                <span>Type-safe API integration</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;;;;;;;;;AAGA;AAAA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;;;;;;;sCAEvD,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAM/D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;;sDACC,8OAAC;4CAAU,WAAU;;8DACnB,8OAAC,kLAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;sDAAgB;;;;;;;;;;;;8CAInB,8OAAC;8CACC,cAAA,8OAAC;wCAAO,OAAO;wCAAC,WAAU;kDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAW;;;;;;;;;;;;;;;;;;;;;;sCAO5B,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;;sDACC,8OAAC;4CAAU,WAAU;;8DACnB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;sDAAgB;;;;;;;;;;;;8CAInB,8OAAC;8CACC,cAAA,8OAAC;wCAAO,OAAO;wCAAC,SAAQ;wCAAU,WAAU;kDAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS5B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}]}