import { create } from 'zustand';
import { crawlApi } from '@/lib/api';
import { CrawlRequest, CrawledData, ChatWithManusRequest } from '@/lib/types';

interface CrawlerState {
  results: CrawledData | null;
  isLoading: boolean;
  error: string | null;
  status: string | null;
  progress: number | undefined;
  currentRequestId: string | null;

  // Actions
  startCrawl: (request: CrawlRequest) => Promise<void>;
  startChat: (request: ChatWithManusRequest) => Promise<void>;
  setResults: (results: CrawledData) => void;
  setStatus: (status: string) => void;
  setProgress: (progress: number) => void;
  setError: (error: string) => void;
  clearError: () => void;
  reset: () => void;
}

export const useCrawlerStore = create<CrawlerState>((set, get) => ({
  results: null,
  isLoading: false,
  error: null,
  status: null,
  progress: undefined,
  currentRequestId: null,

  startCrawl: async (request: CrawlRequest) => {
    set({ 
      isLoading: true, 
      error: null, 
      status: 'Starting crawl...', 
      progress: 0,
      currentRequestId: request.request_id 
    });
    
    try {
      await crawlApi.crawlUrlRealtime(request);
      set({ status: 'Crawl started successfully' });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to start crawl',
        isLoading: false,
        status: null,
        progress: undefined,
      });
    }
  },

  startChat: async (request: ChatWithManusRequest) => {
    set({ 
      isLoading: true, 
      error: null, 
      status: 'Starting chat...', 
      progress: 0,
      currentRequestId: request.request_id 
    });
    
    try {
      await crawlApi.chatWithManusRealtime(request);
      set({ status: 'Chat started successfully' });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to start chat',
        isLoading: false,
        status: null,
        progress: undefined,
      });
    }
  },

  setResults: (results: CrawledData) => {
    set({ results, isLoading: false, progress: 100 });
  },

  setStatus: (status: string) => {
    set({ status });
  },

  setProgress: (progress: number) => {
    set({ progress });
  },

  setError: (error: string) => {
    set({ error, isLoading: false, status: null, progress: undefined });
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set({
      results: null,
      isLoading: false,
      error: null,
      status: null,
      progress: undefined,
      currentRequestId: null,
    });
  },
}));
