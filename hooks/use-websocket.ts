import { useEffect, useRef, useState } from 'react';
import { WebSocketManager } from '@/lib/websocket';
import { WebSocketMessage } from '@/lib/types';

export function useWebSocket(requestId: string | null) {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [error, setError] = useState<string | null>(null);
  const wsManager = useRef<WebSocketManager | null>(null);

  useEffect(() => {
    if (!requestId) return;

    const handleMessage = (message: WebSocketMessage) => {
      setLastMessage(message);
    };

    const handleError = (error: Event) => {
      setError('WebSocket connection error');
      setIsConnected(false);
    };

    wsManager.current = new WebSocketManager(requestId, handleMessage, handleError);
    wsManager.current.connect();

    const checkConnection = setInterval(() => {
      if (wsManager.current) {
        setIsConnected(wsManager.current.isConnected());
      }
    }, 1000);

    return () => {
      clearInterval(checkConnection);
      if (wsManager.current) {
        wsManager.current.disconnect();
        wsManager.current = null;
      }
    };
  }, [requestId]);

  return {
    isConnected,
    lastMessage,
    error,
    clearError: () => setError(null),
  };
}
