# Manus Crawler - NextJS Frontend

Modern NextJS 14+ frontend for Manus.im crawler with realtime updates and interactive chat functionality.

## 🚀 Features

- **NextJS 14+ App Router** with TypeScript
- **Realtime WebSocket updates** for live crawling progress
- **Manus-style UI** with dark theme matching Manus.im
- **Interactive chat** with Manus crawler
- **Chrome profile management** via admin dashboard
- **Type-safe API integration** with Zod validation
- **Responsive design** with Tailwind CSS
- **Modern component architecture** with shadcn/ui

## 📋 Requirements

- Node.js 18+ (recommended: 22.15.0)
- npm or yarn
- Backend API running on `http://localhost:8000` (configurable)

## 🛠️ Installation

1. **<PERSON>lone and setup the project:**
   ```bash
   cd /path/to/your/project
   npm install
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.local.example .env.local
   # Edit .env.local with your settings
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser:**
   Navigate to `http://localhost:3000`

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file with the following variables:

```env
BACKEND_URL=http://localhost:8000
WS_URL=ws://localhost:8000
ADMIN_API_KEY=your_super_secret_key_here
```

### Backend Integration

This frontend is designed to work with the Manus crawler backend API. Ensure your backend supports:

- **REST Endpoints:**
  - `POST /crawl-url-realtime/` - Start realtime crawling
  - `POST /chat-with-manus-realtime/` - Interactive chat
  - `POST /admin/setup-chrome-profile/` - Setup Chrome profiles
  - `GET /admin/list-profiles/` - List Chrome profiles
  - `DELETE /admin/delete-profile/{name}` - Delete Chrome profile

- **WebSocket Endpoint:**
  - `ws://localhost:8000/ws/crawl-status/{request_id}` - Realtime updates

## 🎯 Usage

### 1. Home Page
- Overview of features and navigation
- Quick access to crawler and admin interfaces

### 2. Crawler Interface (`/crawler`)
- **Form-based crawling:** Enter URL and configure options
- **Realtime updates:** See crawling progress via WebSocket
- **Manus-style sidebar:** View tasks and navigation
- **Interactive chat:** Communicate with Manus during crawling
- **Results display:** View crawled data and chat messages

### 3. Admin Dashboard (`/admin`)
- **API Key management:** Configure admin access
- **Chrome profile setup:** Create and manage browser profiles
- **Profile management:** List and delete existing profiles
